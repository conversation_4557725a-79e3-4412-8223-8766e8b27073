services:
  frontend:
    build:
      context: .
      dockerfile: apps/client/Dockerfile
    expose:
      - 3002

  playground:
    build:
      context: .
      dockerfile: apps/playground/Dockerfile
    expose:
      - 3001

  server:
    build:
      context: .
      dockerfile: apps/server/Dockerfile
    expose:
      - 3003

  postgres:
    image: postgres:15
    restart: unless-stopped
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: roottoor123
      POSTGRES_DB: venturevibe
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  minio:
    image: quay.io/minio/minio:latest
    command: server /data --console-address ":9001"
    restart: unless-stopped
    entrypoint: >
      /bin/sh -c '
        isAlive() { curl -sf http://127.0.0.1:9000/minio/health/live; }    # check if <PERSON><PERSON> is alive
        minio $0 "$@" --quiet & echo $! > /tmp/minio.pid                   # start Minio in the background
        while ! isAlive; do sleep 0.1; done                                # wait until <PERSON><PERSON> is alive
        mc alias set minio http://127.0.0.1:9000 root roottoor123          # setup Minio client
        mc mb minio/venturevibe || true                                    # create a test bucket
        mc anonymous set public minio/venturevibe                          # make the test bucket public
        kill -s INT $(cat /tmp/minio.pid) && rm /tmp/minio.pid             # stop Minio
        while isAlive; do sleep 0.1; done                                  # wait until Minio is stopped
        exec minio $0 "$@"                                                 # start Minio in the foreground
      '
    environment:
      MINIO_ROOT_USER: root
      MINIO_ROOT_PASSWORD: roottoor123
    ports:
      - "9000:9000" # S3 API
      - "9001:9001" # MinIO console
    volumes:
      - minio_data:/data

volumes:
  postgres_data:
    name: postgres_data
  minio_data:
    name: minio_data
