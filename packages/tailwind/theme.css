@theme {
  --\*: initial;

  --spacing: 0.125rem;

  --breakpoint-xs: 26.25rem;
  --breakpoint-sm: 40rem;
  --breakpoint-md: 48rem;
  --breakpoint-lg: 64rem;
  --breakpoint-xl: 80rem;
  --breakpoint-2xl: 96rem;

  --breakpoint-desktop: 75rem;
  --breakpoint-tablet: 50.625rem;

  --blur-xs: 4px;
  --blur-sm: 8px;
  --blur-md: 12px;
  --blur-lg: 16px;
  --blur-xl: 24px;
  --blur-2xl: 40px;
  --blur-3xl: 64px;

  --aspect-video: 16 / 9;

  --font-display: 'DM Sans', sans-serif;
  --font-header: '<PERSON><PERSON><PERSON>', sans-serif;

  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  --text-2xs: 0.625rem;
  --text-2xs--line-height: 0.825rem;
  --text-xs: 0.75rem;
  --text-xs--line-height: 1rem;
  --text-sm: 0.875rem;
  --text-sm--line-height: 1.25rem;
  --text-md: 1rem;
  --text-md--line-height: 1.5rem;
  --text-lg: 1.125rem;
  --text-lg--line-height: 1.75rem;
  --text-xl: 1.25rem;
  --text-xl--line-height: 1.75rem;
  --text-2xl: 1.5rem;
  --text-2xl--line-height: 2rem;
  --text-3xl: 1.875rem;
  --text-3xl--line-height: 2.25rem;

  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-4xl: 2rem;
  --radius-full: 100%;
  --radius-max: 2.097rem; /* 33554400px max css value */

  --color-transparent: transparent;

  --color-white: #fff;
  --color-light-gray: #f3f3f3;
  --color-gray: #9ca3af;
  --color-spanish-gray: #939393;

  --color-black: #000;
  --color-dark: #101703;
  --color-stone: #212224;
  --color-dark-gray: #373934;
  --color-neutral: #373737;

  --color-spring-bud: #c5f910;
  --color-spring-stroke: #dde8c6;
  --color-dark-spring: #7cae0e;
  --color-light-spring: #f1f9e7;

  --color-bg: #f6f6f6;
  --color-platinum-stroke: #e8ebe2;

  --color-light-orange: #fff3ed;
  --color-orange-stroke: #f5d0c2;
  --color-orange: #ff4714;
  --color-bright-orange: #ffa401;

  --color-green: #3ee089;

  --color-bright-blue: #335cff;
  --color-dark-blue: #0e121b;
  --color-blue: #018af4;

  --color-danger: #ff2244;
  --color-red: #fb3748;
  --color-dark-red: #d80012;

  /* 
  --color-white: #ffffff;
  --color-black: #000000;
  --color-dark-gray: #212224;
  
  --color-dark-blue: #0E121B; */

  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  --animate-spin: spin 1s linear infinite;

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  --animate-slideDownAndFade: slideDownAndFade 700ms cubic-bezier(0.16, 1, 0.3, 1);
  --animate-slideLeftAndFade: slideLeftAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);
  --animate-slideUpAndFade: slideUpAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);
  --animate-slideRightAndFade: slideRightAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1);
  --animate-slideDown: slideDown 300ms cubic-bezier(0.87, 0, 0.13, 1);
  --animate-slideUp: slideUp 300ms cubic-bezier(0.87, 0, 0.13, 1);
  --animate-appearUp: appearUp 0.6s cubic-bezier(0.5, 1, 0.89, 1) forwards;
  --animate-appearRight: appearRight 0.6s cubic-bezier(0.5, 1, 0.89, 1) forwards;
  --animate-appearLeft: appearLeft 0.6s cubic-bezier(0.5, 1, 0.89, 1) forwards;
  --animate-appearUpLate: appearUp 0.6s cubic-bezier(0.5, 1, 0.89, 1) 0.2s forwards;
  --animate-appearRightLate: appearRight 0.6s cubic-bezier(0.5, 1, 0.89, 1) 0.2s forwards;
  --animate-appearLeftLate: appearLeft 0.6s cubic-bezier(0.5, 1, 0.89, 1) forwards;

  @keyframes slideDownAndFade {
    from {
      opacity: 0;
      transform: translateY(-2px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideLeftAndFade {
    from {
      opacity: 0;
      transform: translateX(2px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideUpAndFade {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideRightAndFade {
    from {
      opacity: 0;
      transform: translateX(-2px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideDown {
    from {
      height: 0px;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes slideUp {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0px;
    }
  }

  @keyframes appearUp {
    from {
      opacity: 0;
      transform: translateY(40px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes appearRight {
    from {
      opacity: 0;
      transform: translateX(-40px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes appearLeft {
    from {
      opacity: 0;
      transform: translateX(40px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
}
