import type { FC, SVGProps } from 'react'

export const InterfaceIcon: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = 2, ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width={20}
    height={20}
    viewBox='0 0 28 28'
    strokeWidth={strokeWidth}
    fill='none'
    {...props}>
    <title> Interface </title>
    <path
      stroke='currentColor'
      strokeWidth='1.5'
      d='M2.918 14c0-5.225 0-7.837 1.623-9.46 1.623-1.623 4.236-1.623 9.46-1.623 5.225 0 7.837 0 9.46 1.623 1.624 1.623 1.624 4.235 1.624 9.46 0 5.225 0 7.837-1.623 9.46-1.624 1.623-4.236 1.623-9.46 1.623-5.225 0-7.838 0-9.46-1.623-1.624-1.623-1.624-4.235-1.624-9.46Z'
    />
    <path stroke='currentColor' strokeLinejoin='round' strokeWidth='1.5' d='M2.918 10.5h22.167' />
    <path
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      d='M15.168 15.167h4.667m-4.667 4.666h2.333'
    />
    <path
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='2'
      d='M8.164 7h.009m4.663 0h.009'
    />
    <path stroke='currentColor' strokeLinecap='round' strokeLinejoin='round' strokeWidth='1.5' d='M10.5 10.5v14.583' />
  </svg>
)
