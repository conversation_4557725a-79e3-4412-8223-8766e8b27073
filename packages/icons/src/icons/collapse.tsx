import type { FC, SVGProps } from 'react'

export const CollapseIcon: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = 2, ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    viewBox='0 0 24 24'
    fill='none'
    width={20}
    height={20}
    strokeWidth={strokeWidth}
    {...props}>
    <title>Collapse</title>
    <line
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      x1='5'
      y1='12'
      x2='19'
      y2='12'
    />
  </svg>
)
