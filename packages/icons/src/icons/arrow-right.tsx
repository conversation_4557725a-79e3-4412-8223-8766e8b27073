import type { FC, SVGProps } from 'react'

const ArrowRight: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = '2', ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    viewBox='0 0 24 24'
    fill='none'
    stroke='currentColor'
    strokeWidth={strokeWidth}
    strokeLinecap='round'
    strokeLinejoin='round'
    {...props}>
    <title>Arrow Right</title>
    <path d='M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3' />
  </svg>
)

export { ArrowRight }
