import type { FC, SVGProps } from 'react'

type HamburgerMenuIconProps = SVGProps<SVGSVGElement> & {
  isOpened?: boolean
}

export const HamburgerIcon: FC<HamburgerMenuIconProps> = ({ strokeWidth = 2, isOpened = false, ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    viewBox='0 0 100 100'
    width={24}
    height={24}
    stroke='currentColor'
    strokeWidth={strokeWidth}
    strokeLinecap='round'
    {...props}>
    <title> Hamburger Menu Icon </title>
    <line
      x1='10'
      x2='90'
      y1='25'
      y2='25'
      style={{
        transition: 'transform 0.3s',
        transformOrigin: '50px 25px',
        transform: isOpened ? 'translateY(25px) rotate(45deg)' : 'none',
      }}
    />
    <line
      x1='10'
      x2='90'
      y1='50'
      y2='50'
      style={{
        transition: 'transform 0.3s, opacity 0.3s',
        transform: isOpened ? 'translateX(100px)' : 'translateX(0)',
        opacity: isOpened ? 0 : 1,
      }}
    />
    <line
      x1='10'
      x2='90'
      y1='75'
      y2='75'
      style={{
        transition: 'transform 0.3s',
        transformOrigin: '50px 75px',
        transform: isOpened ? 'translateY(-25px) rotate(-45deg)' : 'none',
      }}
    />
  </svg>
)
