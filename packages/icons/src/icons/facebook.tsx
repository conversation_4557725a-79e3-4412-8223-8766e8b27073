import type { FC, SVGProps } from 'react'

export const FacebookIcon: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = 2, ...props }) => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={20}
      height={20}
      viewBox='37.29 0 80.559 155.139'
      strokeWidth={strokeWidth}
      {...props}>
      <title>Facebook Icon</title>
      <path
        d='M89.584 155.139V84.378h23.742l3.562-27.585H89.584V39.184c0-7.984 2.208-13.425 13.67-13.425l14.595-.006V1.08C115.325.752 106.661 0 96.577 0 75.52 0 61.104 12.853 61.104 36.452v20.341H37.29v27.585h23.814v70.761z'
        fill='currentColor'
      />
    </svg>
  )
}
