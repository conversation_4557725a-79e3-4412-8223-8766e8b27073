import { type FC, type SVGProps, useId } from 'react'

export const CheckboxIcon: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = 2, ...props }) => {
  const clipPathId = useId()

  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={20}
      height={20}
      viewBox='0 0 24 24'
      strokeWidth={strokeWidth}
      fill='none'
      {...props}>
      <title> Checkbox </title>
      <g
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.5'
        clipPath={`url(#${clipPathId})`}>
        <path d='m9 12 2 2 4-4' />
        <path d='M12 3c7.2 0 9 1.8 9 9s-1.8 9-9 9-9-1.8-9-9 1.8-9 9-9Z' />
      </g>
      <defs>
        <clipPath id={clipPathId}>
          <path d='M0 0h24v24H0z' />
        </clipPath>
      </defs>
    </svg>
  )
}
