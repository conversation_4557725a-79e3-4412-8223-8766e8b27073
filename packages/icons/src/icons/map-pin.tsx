import type { FC, SVGProps } from 'react'

const MapPin: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = '2', ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    viewBox='0 0 24 24'
    fill='none'
    stroke='currentColor'
    strokeWidth={strokeWidth}
    strokeLinecap='round'
    strokeLinejoin='round'
    {...props}>
    <title>Map Pin</title>
    <path
      strokeWidth='1.5'
      d='M13.618 21.867A2.366 2.366 0 0 1 12 22.5a2.366 2.366 0 0 1-1.617-.633C6.412 18.126 1.09 13.947 3.685 7.88 5.09 4.6 8.458 2.5 12.001 2.5c3.543 0 6.912 2.1 8.315 5.38 2.592 6.06-2.717 10.259-6.698 13.987Z'
    />
    <path strokeWidth='1.5' d='M15.5 11.5a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Z' />
  </svg>
)

export { MapPin }
