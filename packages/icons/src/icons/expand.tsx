import type { FC, SVGProps } from 'react'

export const ExpandIcon: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = 2, ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    viewBox='0 0 24 24'
    width={20}
    height={20}
    fill='none'
    stroke='currentColor'
    strokeWidth={strokeWidth}
    {...props}>
    <title>Expand</title>
    <line strokeLinecap='round' strokeLinejoin='round' x1='12' y1='5' x2='12' y2='19' />
    <line strokeLinecap='round' strokeLinejoin='round' x1='5' y1='12' x2='19' y2='12' />
  </svg>
)
