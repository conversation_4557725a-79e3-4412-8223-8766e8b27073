import type { FC, SVGProps } from 'react'

export const ChevronDownIcon: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = 2, ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width={20}
    height={20}
    viewBox='0 0 28 28'
    strokeWidth={strokeWidth}
    fill='none'
    {...props}>
    <title> Chevron Down </title>
    <polyline stroke='currentColor' strokeLinecap='round' strokeLinejoin='round' points='6 9 12 15 18 9' />
  </svg>
)
