import type { FC, SVGProps } from 'react'

const Plus: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = '2', ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    viewBox='0 0 24 24'
    fill='none'
    stroke='currentColor'
    strokeWidth={strokeWidth}
    strokeLinecap='round'
    strokeLinejoin='round'
    {...props}>
    <title>Plus</title>
    <path d='M12 4.5v15m7.5-7.5h-15' />
  </svg>
)

export { Plus }
