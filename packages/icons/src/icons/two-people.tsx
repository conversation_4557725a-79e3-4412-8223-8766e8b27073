import type { FC, SVGProps } from 'react'

export const TwoPeopleIcon: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = 2, ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width={20}
    height={20}
    viewBox='0 0 28 28'
    strokeWidth={strokeWidth}
    fill='none'
    {...props}>
    <title> Two People </title>
    <path
      stroke='currentColor'
      strokeLinecap='round'
      strokeWidth='1.5'
      d='M21.718 23.333h.571c1.342 0 2.409-.611 3.367-1.465 2.434-2.172-3.287-4.368-5.24-4.368M18.081 5.913c.265-.052.54-.08.822-.08 2.123 0 3.845 1.567 3.845 3.5s-1.721 3.5-3.845 3.5a4.19 4.19 0 0 1-.822-.08'
    />
    <path
      stroke='currentColor'
      strokeWidth='1.5'
      d='M5.228 18.796c-1.375.738-4.982 2.243-2.785 4.126 1.073.92 2.268 1.578 3.77 1.578h8.574c1.502 0 2.697-.658 3.77-1.578 2.197-1.883-1.41-3.389-2.785-4.126-3.226-1.728-7.318-1.728-10.544 0ZM15.165 8.75a4.667 4.667 0 1 1-9.333 0 4.667 4.667 0 0 1 9.333 0Z'
    />
  </svg>
)
