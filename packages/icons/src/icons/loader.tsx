import type { FC, SVGProps } from 'react'

const Loader: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = '2', ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    viewBox='0 0 24 24'
    fill='none'
    stroke='currentColor'
    strokeWidth={strokeWidth}
    strokeLinecap='round'
    strokeLinejoin='round'
    {...props}>
    <title>Loading</title>
    <path d='M12 2a10 10 0 1 0 10 10' />
  </svg>
)

export { Loader }
