import type { FC, SVGProps } from 'react'

export const ApprovalTickIcon: FC<SVGProps<SVGSVGElement>> = ({ strokeWidth = 2, ...props }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width={20}
    height={20}
    viewBox='0 0 28 28'
    strokeWidth={strokeWidth}
    fill='none'
    {...props}>
    <title> Approval Tick </title>
    <path
      stroke='currentColor'
      strokeWidth='1.5'
      d='M2.914 14c0-5.225 0-7.837 1.623-9.46 1.623-1.623 4.236-1.623 9.46-1.623 5.225 0 7.837 0 9.46 1.623 1.624 1.623 1.624 4.235 1.624 9.46 0 5.225 0 7.837-1.623 9.46-1.623 1.623-4.236 1.623-9.46 1.623-5.225 0-7.838 0-9.46-1.623-1.624-1.623-1.624-4.235-1.624-9.46Z'
    />
    <path
      stroke='currentColor'
      strokeLinecap='round'
      strokeLinejoin='round'
      strokeWidth='1.5'
      d='M9.332 16.042s1.867 1.064 2.8 2.625c0 0 2.8-6.125 6.533-8.167'
    />
  </svg>
)
