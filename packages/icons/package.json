{"devDependencies": {"@ozaco/cli": "^0.0.14", "@types/react": "^19.1.9", "clsx": "^2.1.1", "react": "^19.1.1", "typescript": "^5.9.2"}, "exports": {".": {"default": "./dist/index.js", "source": "./src/index.ts", "types": "./dist/index.d.ts"}}, "files": ["dist"], "name": "@venture-vibe/icons", "peerDependencies": {"clsx": ">= 2.1.1", "react": ">= 19.1.0", "typescript": ">= 5.8.3"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "private": true, "tsx-exports": ["default"], "type": "module", "version": "0.0.0"}