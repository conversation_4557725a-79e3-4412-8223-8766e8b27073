language: "typescript"
type: "library"
platform: "bun"

tasks:
  dev:
    local: true
    command: "bunx ozaco build -t browser -w"
    deps: ["icons:dev", "utils:dev"]
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
  build:
    command: "bunx ozaco build -t browser -s --env production"
    deps: ["icons:build", "utils:build"]
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
