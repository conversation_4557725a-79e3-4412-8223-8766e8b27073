'use client'

import { Content, List, Root, Trigger } from '@radix-ui/react-tabs'
import { cva, type VariantProps } from '@venture-vibe/utils'
import { type FC, type ReactNode, useMemo } from 'react'

const createTriggerVariants = cva(
  [
    'h-[3rem]',
    'px-5',
    'flex-1',
    'flex',
    'items-center',
    'justify-center',
    'select-none',
    'rounded-t-lg',
    'data-[state=active]:shadow-[inset_0_-1px_0_0,0_1px_0_0]',
    'data-[state=active]:shadow-current',
    'data-[state=active]:focus:relative',
    'data-[state=active]:focus:shadow-[0_0_0_2px]',
    'data-[state=active]:focus:shadow-current',
  ],
  {
    variants: {
      size: {
        primary: 'h-[2.75rem]',
        large: 'h-[3rem]',
        small: 'h-[2.25rem]',
      },
      color: {
        primary: ['text-[#baa7ff]', 'data-[state=active]:text-current]'],
        secondary: ['text-[#baa7ff]', 'data-[state=active]:text-current]'],
        tertiary: ['text-[#baa7ff]', 'data-[state=active]:text-current]'],
      },
    },
    defaultVariants: {
      size: 'primary',
      color: 'primary',
    },
  },
)

export const createTabsVariants = cva(['flex', 'flex-col', 'rounded-lg'], {
  variants: {
    size: {
      primary: 'max-w-[16rem]',
      large: 'max-w-[18rem]',
      small: 'max-w-[12rem]',
    },
    color: {
      primary: 'bg-[#ffffff]',
      secondary: 'bg-[#ffffffcc]',
      tertiary: 'bg-[#ffffffaa]',
    },
  },
  defaultVariants: {
    size: 'primary',
    color: 'primary',
  },
})

export interface TabsEntry {
  id: number
  title: string | ReactNode
  content: ReactNode
}

export interface TabsCustomProps {
  entries: TabsEntry[]
}

export type TabsProps = TabsCustomProps & VariantProps<typeof createTabsVariants> & { className?: string }

export const Tabs: FC<TabsProps> = ({ size, color, preset, entries, className, ...rest }): ReactNode => {
  const tabsStyles = createTabsVariants({ size, color, preset })
  const triggerStyles = createTriggerVariants({ size, color, preset })

  const triggers = useMemo(
    () =>
      entries.map(entry => (
        <Trigger key={entry.id} value={entry.id.toString()} className={triggerStyles}>
          {entry.title}
        </Trigger>
      )),
    [triggerStyles, entries],
  )

  const contents = useMemo(
    () =>
      entries.map(entry => (
        <Content
          key={entry.id}
          value={entry.id.toString()}
          className='flex flex-grow items-center justify-center overflow-x-auto p-5 outline-none'>
          {entry.content}
        </Content>
      )),
    [entries],
  )

  const firstTabId = useMemo(() => entries.at(0)?.id.toString() ?? '', [entries])

  return (
    <Root className={tabsStyles} defaultValue={firstTabId} {...rest}>
      <List className='flex shrink-0'>{triggers}</List>
      {contents}
    </Root>
  )
}
