// import { ArrowRight } from '@venture-vibe/icons'
import { cva, type VariantProps } from '@venture-vibe/utils'
import type { FC, ReactNode } from 'react'
import { Link } from './link' // aktif ettik
import { Text } from './text'
import { Title } from './title'

const projectCardVariants = cva('max-w-220 flex flex-col items-end rounded-3xl p-4 md:p-8 gap-10 h-full', {
  variants: {
    color: {
      primary: 'bg-spring-bud/25',
      secondary: 'bg-red',
      green: 'bg-dark-spring',
      blue: 'bg-dark-blue',
      orange: 'bg-orange',
      'light-spring': 'bg-white',
      'light-orange': 'bg-light-orange',
    },
  },
  defaultVariants: {
    color: 'primary',
  },
})

const innerProjectCardVariants = cva('flex flex-col items-start p-6 md:p-10 gap-10 rounded-2xl flex-grow', {
  variants: {
    color: {
      primary: 'bg-stone',
      secondary: 'bg-dark-red',
      green: 'bg-dark-gray',
      blue: 'bg-blue',
      orange: 'bg-dark-red',
      'light-spring': 'bg-light-spring/40',
      'light-orange': 'bg-white',
    },
  },
  defaultVariants: {
    color: 'primary',
  },
})

interface ProjectCardProps extends VariantProps<typeof projectCardVariants> {
  title: string
  description: string
  badge: ReactNode
  tags: string[]
  buttonText: string
  link?: string
}

const ProjectCard: FC<ProjectCardProps> = ({ title, description, badge, tags, color, link }) => {
  const isLight = color && ['light-spring', 'light-orange'].includes(color)

  const titleColor = isLight ? 'black' : 'white'
  const textColorClassName = isLight ? 'text-dark-gray' : 'text-light-gray'

  let tagClassName = 'bg-neutral text-white text-sm rounded-sm px-4 py-1'
  if (color === 'light-spring') {
    tagClassName = 'bg-spring-stroke text-dark-spring text-sm rounded-sm px-4 py-1'
  } else if (color === 'light-orange') {
    tagClassName = 'bg-orange-stroke text-orange text-sm rounded-sm px-4 py-1'
  }

  const content = (
    <div className={`${projectCardVariants({ color })} group`}>
      <div className={innerProjectCardVariants({ color })}>
        <div className='flex flex-col items-start flex-grow gap-10'>
          {badge}
          <Title as='h1' size='xl' color={titleColor} className='w-full hover:underline underline-offset-4'>
            {title}
          </Title>
          <Text size='sm' className={`${textColorClassName} w-full hover:underline underline-offset-4`}>
            {description}
          </Text>
        </div>

        {tags.length > 0 && (
          <div className='flex flex-row flex-wrap gap-3 w-full'>
            {tags.map(tag => (
              <div key={tag} className={tagClassName}>
                {tag}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )

  return link ? <Link href={link}>{content}</Link> : content
}

export { ProjectCard, type ProjectCardProps, projectCardVariants }
