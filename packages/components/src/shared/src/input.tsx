'use client'
import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC } from 'react'

const inputVariants = cva(
  [
    'border transition-all duration-200 ease-in-out',
    'group focus-within',
    '[&>input]:!outline-none [&>input]:focus:!outline-none',
    '[&>input]:flex-1',
    '[&]:flex [&]:items-center',
    '[&>[data-side="left"]]:order-1',
    '[&>input]:order-2',
    '[&>[data-side="right"]]:order-3',
  ],
  {
    variants: {
      variant: {
        primary: [
          'border-light-gray bg-white',
          'hover:border-light-gray',
          'focus-within:ring-light-gray focus-within:border-light-gray',
          '[&>input]:placeholder:text-spanish-gray',
        ],
        secondary: [
          'bg-transparent border-light-gray text-white',
          'hover:border-gray',
          'focus-within:ring-gray focus-within:border-gray',
          '[&>input]:placeholder:text-white',
        ],
        tertiary: [
          'border-light-gray bg-white',
          'hover:border-light-gray',
          'focus-within:ring-light-gray focus-within:border-light-gray',
          '[&>input]:placeholder:text-dark',
        ],
      },
      size: {
        sm: 'h-12 px-3 py-10 text-2xs',
        md: 'h-16 px-4 py-10 text-xs',
        lg: 'h-24 px-5 text-md',
      },
      roundness: {
        xl: 'rounded-xl',
        '2xl': 'rounded-2xl',
        max: 'rounded-max',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'lg',
      roundness: 'xl',
    },
  },
)

interface InputProps
  extends VariantProps<typeof inputVariants>,
    Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  icon?: React.ReactNode
  iconSide?: 'left' | 'right'
}

const Input: FC<InputProps> = ({
  className,
  variant,
  size,
  roundness,
  disabled,
  icon,
  iconSide = 'left',
  ...props
}) => {
  return (
    <div className={clsx(inputVariants({ size, variant, roundness, className }), disabled && 'opacity-50')}>
      {icon && iconSide === 'left' && (
        <div className='flex items-center pr-2' data-side='left'>
          {icon}
        </div>
      )}
      <input
        className='w-full h-full bg-transparent px-4 outline-none placeholder:text-current disabled:cursor-not-allowed'
        disabled={disabled}
        {...props}
      />
      {icon && iconSide === 'right' && (
        <div className='flex items-center pl-2' data-side='right'>
          {icon}
        </div>
      )}
    </div>
  )
}

const TextArea: FC<React.TextareaHTMLAttributes<HTMLTextAreaElement> & { isResizable?: boolean }> = ({
  className,
  isResizable = true,
  ...props
}) => {
  return (
    <textarea
      className={clsx(
        'w-full h-48 p-8 text-md border border-light-gray rounded-5xl bg-white focus:ring-light-gray focus:border-light-gray outline-none',
        isResizable ? 'resize' : 'resize-none',
        className,
      )}
      {...props}
    />
  )
}

export { Input, TextArea, type InputProps, inputVariants }
