import NextLink from 'next/link'
import type { FC, ReactNode } from 'react'

interface LinkProps {
  href: string
  openInNewTab?: boolean
  children: ReactNode
  className?: string
}

const EXTERNAL_LINK_REGEX = /^https?:\/\//

const Link: FC<LinkProps> = ({ href, openInNewTab, children, className }) => {
  const isExternal = EXTERNAL_LINK_REGEX.test(href)

  if (isExternal) {
    return (
      <a href={href} className={className} target={openInNewTab ? '_blank' : undefined}>
        {children}
      </a>
    )
  }

  return (
    <NextLink href={href} className={className} target={openInNewTab ? '_blank' : undefined}>
      {children}
    </NextLink>
  )
}

export { Link, type LinkProps }
