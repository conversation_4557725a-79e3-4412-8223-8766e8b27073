'use client'

import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC } from 'react'
import { BackgroundSection } from './background-section'
import { ImageMask } from './image-mask'
import { Text } from './text'
import { Title } from './title'

export const createToolCardVariants = cva(
  [
    'font-display',
    'rounded-3xl',
    'p-8',
    'font-display',
    'w-full',
    'h-full',
    'min-h-[300px]',
    'h-[300px]',
    'max-w-full',
  ],
  {
    variants: {
      size: {
        lg: ['lg:p-12', 'lg:min-h-[640px]', 'lg:h-[640px]', 'lg:min-w-[320px]', 'lg:max-w-[640px]'],
        sm: '',
      },
    },
    defaultVariants: {
      size: 'sm',
    },
  },
)

export interface ToolCardCustomProps {
  type: string
  title: string
  description: string
  imageHref: string
  backgroundColor: string
  backgroundMaskColor: string
}

export type ToolCardProps = VariantProps<typeof createToolCardVariants> & ToolCardCustomProps

export const ToolCard: FC<ToolCardProps> = props => {
  const { type, title, description, imageHref, backgroundColor, backgroundMaskColor } = props

  return (
    <div style={{ backgroundColor }} className={createToolCardVariants(props)}>
      <BackgroundSection
        className='w-full h-full'
        as='div'
        layers={[
          {
            type: 'node',
            node: <ImageMask imageHref={imageHref} maskColor={backgroundMaskColor} blur />,
            className: 'flex items-center justify-center',
          },
          {
            type: 'node',
            node: (
              <div className='w-full h-full flex flex-col justify-between'>
                <div className='flex justify-between'>
                  <Title
                    as='h3'
                    color='white'
                    size={null}
                    thickness='normal'
                    className={clsx('text-[20px]', { 'lg:text-[22px]': props.size === 'lg' })}>
                    {type}
                  </Title>
                  <div
                    className={clsx('w-[64px] h-[64px] p-4 bg-bg rounded-max', {
                      'lg:w-[72px] lg:h-[72px]': props.size === 'lg',
                    })}>
                    <ImageMask imageHref={imageHref} maskColor={backgroundColor} />
                  </div>
                </div>
                <div className='w-full h-fit flex flex-col gap-4 pb-8'>
                  <Title
                    as='h2'
                    color='white'
                    size={null}
                    thickness='medium'
                    className={clsx('text-[26px]', { 'lg:text-[30px]': props.size === 'lg' })}>
                    {title}
                  </Title>
                  <Text
                    color={null}
                    size={null}
                    className={clsx('text-[#e9e9e9] text-[18px] leading-[24px] line-clamp-4', {
                      'lg:text-[22px] lg:leading-[28px]': props.size === 'lg',
                    })}>
                    {description}
                  </Text>
                </div>
              </div>
            ),
          },
        ]}
      />
    </div>
  )
}
