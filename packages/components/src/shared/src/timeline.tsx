import { clsx } from '@venture-vibe/utils'
import type { FC, HTMLAttributes } from 'react'
import { Appear } from './appear'
import { Badge } from './badge'
import { Link } from './link'
import { Text } from './text'
import { Title } from './title'

interface Event {
  date: string
  title: string
  description: string
  location: string
  tags: string[]
  slug: string
}

interface TimelineProps extends HTMLAttributes<HTMLDivElement> {
  items?: Event[]
}
interface TimelineItemProps {
  item: Event
  side: 'left' | 'right'
  isFirst: boolean
  isLast: boolean
}

const TimelineItem: FC<TimelineItemProps> = ({ item, side, isFirst, isLast }) => {
  const isRightSide = side === 'right'
  const sideClasses = {
    container: isRightSide
      ? 'border-t-2 border-r-2 rounded-tr-xl items-end ml-4 pl-16'
      : 'border-l-2 border-t-2 rounded-tl-xl items-start pr-20',
    card: isRightSide ? 'bg-light-spring border-spring-stroke mr-10' : 'bg-light-orange border-orange-stroke ml-10',
    dot: isRightSide ? 'bg-light-spring border-spring-stroke' : 'bg-light-orange border-orange-stroke',
    connector: isRightSide ? 'absolute -right-5.5' : 'absolute -left-5.5',
    dateContainer: isRightSide ? 'absolute left-full ml-5' : 'absolute right-full mr-5',
    dateLine: isRightSide ? 'after:bg-spring-stroke after:-left-6' : 'after:bg-orange-stroke after:-right-6',
  }

  return (
    <Link href={`/activities/${item.slug}`} className='w-full' key={item.slug}>
      <Appear late className='w-full'>
        <div
          className={clsx(
            'box-border border-platinum-stroke w-full relative z-10 flex flex-col justify-center',
            sideClasses.container,
            !isFirst && 'mt-3',
            {
              "after:content-[''] after:absolute after:w-4 after:h-24 after:border-platinum-stroke after:border-b-2 after:border-l-2 after:rounded-bl-xl after:top-[-3rem] after:left-[-0.5rem] before:content-[''] before:absolute before:top-[-4rem] before:left-[-1.025rem] before:w-10 before:h-10 before:z-20 before:border-2 before:border-platinum-stroke before:bg-light-gray before:rounded-full":
                isFirst,
              "after:content-[''] after:absolute after:top-[-0.5rem] after:w-4 after:h-4 after:border-platinum-stroke":
                !isFirst,
              'after:border-b-2 after:border-l-2 after:rounded-bl-xl after:left-[-0.5rem]': !isFirst && isRightSide,
              'after:border-b-2 after:border-r-2 after:rounded-br-xl after:right-[-0.45rem]': !(isFirst || isRightSide),
              "before:content-[''] before:absolute before:bottom-[-1rem] before:w-10 before:h-10 before:z-20 before:border-2 before:border-platinum-stroke before:bg-light-gray before:rounded-full":
                isLast,
              'before:right-[-0.7rem]': isLast && isRightSide,
              'before:left-[-0.7rem]': isLast && !isRightSide,
            },
          )}>
          <div
            className={clsx(
              'w-full lg:max-w-2/4 gap-4 border h-fit px-10 py-8 rounded-lg mt-10 mb-7 flex flex-col justify-center items-start',
              sideClasses.card,
            )}>
            <div className='flex w-full flex-row items-center justify-between'>
              <Title as='h1' size={null} className='text-sm md:text-lg'>
                {item.title}
              </Title>
              <Text as='span' size='sm' className='whitespace-nowrap'>
                {item.location}
              </Text>
            </div>
            <Text as='p' size={null} className='text-start text-xs md:text-sm'>
              {item.description}
            </Text>

            <div className='w-full flex flex-row justify-between items-center'>
              <div className='flex flex-row gap-4'>
                {item.tags.map(tag => (
                  <Badge
                    variant='quaternary-border'
                    key={tag}
                    size={null}
                    text={tag}
                    className='border-none px-0 text-2xs sm:text-xs'
                  />
                ))}
              </div>
              <Text as='span' size='sm' thickness='medium' className='lg:hidden'>
                {item.date}
              </Text>
            </div>
          </div>
          <div className={clsx('top-1/2 -translate-y-1/2 items-center flex', sideClasses.connector)}>
            <div className={clsx('h-10 w-10 rounded-full border-2', sideClasses.dot)} />
            <Text
              as='span'
              size='sm'
              thickness='medium'
              className={clsx(
                'hidden',
                'lg:flex',
                'top-1/2 -translate-y-1/2 whitespace-nowrap rounded-lg border p-2',
                'after:content-[""] after:absolute after:top-7 after:h-1 after:w-6',
                sideClasses.card,
                sideClasses.dateContainer,
                sideClasses.dateLine,
              )}>
              {item.date}
            </Text>
          </div>
        </div>
      </Appear>
    </Link>
  )
}

const Timeline: FC<TimelineProps> = ({ items = [] }) => {
  return (
    <div className='flex w-full flex-col items-center'>
      {items.map((item, index) => {
        const isFirst = index === 0
        const isLast = index === items.length - 1
        const side = index % 2 === 0 ? 'right' : 'left'

        return <TimelineItem key={item.slug} item={item} side={side} isFirst={isFirst} isLast={isLast} />
      })}
    </div>
  )
}

export { Timeline, type Event, type TimelineProps }
