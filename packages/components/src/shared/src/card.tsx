import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import { type FC, memo, type ReactNode } from 'react'
import { Link } from './link'
import { Text } from './text'
import { Title } from './title'

const MAX_TAG_SIZE = 3
const MAX_DESCRIPTION_LENGTH = 150

const cardVariants = cva(
  [
    'rounded-2xl',
    'flex flex-col',
    'font-display',
    'group',
    'hover:cursor-pointer',
    'border-[1px]',
    'border-platinum-stroke',
    'w-full',
  ],
  {
    variants: {
      variant: {
        primary: 'bg-white',
        secondary: 'bg-light-spring',
        tertiary: 'bg-light-orange',
        quaternary: 'bg-transparent',
      },
    },
    defaultVariants: {
      variant: 'primary',
    },
  },
)

type BaseProps = VariantProps<typeof cardVariants> & {
  image: string
  title: string
  description: string
  link?: string
  badge?: ReactNode
}

type CardProps =
  | (BaseProps & {
      type?: 'primary'
      tags?: string[]
      date?: string
      writer?: string
      hoverComponent?: never
      showTexts?: never
    })
  | (BaseProps & {
      type?: 'secondary'
      tags?: never
      date?: never
      writer?: never
      hoverComponent?: ReactNode
      showTexts?: boolean
    })

const Card: FC<CardProps> = memo(
  ({
    type = 'primary',
    image,
    title,
    description,
    tags = [],
    date,
    writer,
    badge,
    variant,
    hoverComponent,
    showTexts = true,
    link = '',
  }) => {
    return (
      <Link href={link} className='flex w-full max-w-220 sm:max-w-260 lg:max-w-300'>
        {type === 'primary' && (
          <div className={clsx(cardVariants({ variant }))}>
            <div className={clsx('overflow-hidden', 'rounded-t-2xl')}>
              <div
                className={clsx(
                  'h-125',
                  'rounded-t-2xl',
                  'bg-cover',
                  'bg-center',
                  'transition-transform',
                  'duration-500',
                  'group-hover:scale-110',
                )}
                style={{ backgroundImage: `url(${image})` }}
              />
            </div>
            <div className={clsx('pt-7 pr-8 pb-9 pl-8', 'flex flex-col', 'flex-grow')}>
              <div className={clsx('flex-grow')}>
                {tags?.length > 0 && (
                  <div className={clsx('mb-2', 'flex items-center justify-start gap-4')}>
                    {tags.slice(0, MAX_TAG_SIZE).map((tag, index) => (
                      <Text key={String(index)} as='span' size='xs' className={clsx('text-dark-gray')}>
                        {tag}
                      </Text>
                    ))}
                  </div>
                )}
                <div className={clsx('flex items-center justify-between', 'mb-2')}>
                  <Title as='h6' thickness='medium' size='lg'>
                    {title}
                  </Title>
                  {badge && <div>{badge}</div>}
                </div>
                <Text as='p' thickness='normal' size='xs' className={clsx('text-dark-gray')}>
                  {description.length > MAX_DESCRIPTION_LENGTH
                    ? `${description.slice(0, MAX_DESCRIPTION_LENGTH)}...`
                    : description}
                </Text>
              </div>
              <div className={clsx('flex items-end justify-between', 'mt-2')}>
                <Text as='span' size='xs' className={clsx('text-dark-gray')}>
                  {writer}
                </Text>
                <Text as='span' size='xs' className={clsx('text-dark-gray')}>
                  {date}
                </Text>
              </div>
            </div>
          </div>
        )}
        {type === 'secondary' && (
          <div className={clsx(cardVariants({ variant }))}>
            <div
              className={clsx(
                'overflow-hidden',
                'rounded-2xl',
                showTexts ? 'p-4' : '',
                variant === 'quaternary' ? 'bg-white' : '',
              )}>
              <div className={clsx('relative', 'rounded-2xl', 'overflow-hidden')}>
                <div
                  className={clsx('h-125', 'rounded-2xl', 'bg-cover', 'bg-center')}
                  style={{ backgroundImage: `url(${image})` }}
                />
                <div
                  className={clsx(
                    'absolute inset-0',
                    'bg-black',
                    'rounded-2xl',
                    'transition-opacity',
                    'duration-800',
                    'opacity-0',
                    'group-hover:opacity-10',
                  )}
                />
                {hoverComponent && (
                  <div
                    className={clsx(
                      'absolute inset-0',
                      'flex',
                      'items-end',
                      'justify-center',
                      'pb-4',
                      'opacity-0',
                      'group-hover:opacity-100',
                      'transition-opacity',
                      'duration-800',
                      'pointer-events-none',
                    )}>
                    <div className={clsx('bg-white', 'rounded-lg', 'px-4 py-4')}>{hoverComponent}</div>
                  </div>
                )}
              </div>
            </div>
            {showTexts && (
              <div className={clsx('pt-4 pr-20 pb-4 pl-13', 'flex flex-col')}>
                <div>
                  <div className={clsx('flex items-center justify-start gap-3', 'mb-2')}>
                    <Title as='h6' thickness='medium' size='lg'>
                      {title}
                    </Title>
                    {badge && <div className={clsx('flex items-center', 'h-full')}>{badge}</div>}
                  </div>
                  <Text as='p' thickness='normal' size='xs' className={clsx('text-dark-gray')}>
                    {description}
                  </Text>
                </div>
              </div>
            )}
          </div>
        )}
      </Link>
    )
  },
)

export { Card, type CardProps, cardVariants }
