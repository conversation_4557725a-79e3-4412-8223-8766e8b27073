'use client'

import { Separator } from '@radix-ui/react-separator'
import { CheckboxIcon } from '@venture-vibe/icons'
import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC, ReactNode } from 'react'
import { Text } from './text'
import { Title } from './title'

/** In website, this is a non-clickable button */
export const createLabelVariants = cva(['rounded-[5rem]'], {
  variants: {
    size: {
      primary: ['gap-3.5', 'py-6', 'px-12'],
      small: ['gap-3.5', 'py-4', 'px-8'],
    },
    color: {
      primary: ['bg-[#ADFF00]'],
      secondary: ['bg-[#7521FB]', 'text-white'],
      tertiary: ['bg-[#FF7C33]', 'text-white'],
    },
  },
  defaultVariants: {
    size: 'primary',
    color: 'primary',
  },
})

export const createPricingCardVariants = cva(
  ['bg-[#FFFFFF]', 'rounded-3xl', 'flex', 'flex-row', 'flex-wrap', 'font-display', 'max-w-[25.625rem]', 'max-h-fit'],
  {
    variants: {
      size: {
        primary: ['px-16', 'py-20', 'gap-20'],
        small: ['px-8', 'py-10', 'gap-12'],
      },
      color: {
        primary: [],
        secondary: [],
        tertiary: [],
      },
    },
    defaultVariants: {
      size: 'primary',
      color: 'primary',
    },
  },
)

export interface PricingCardCustomProps {
  planType: string
  description: string
  features: string[]
  price?: number | string
  discount?: number | string
  button: ReactNode
}

export type PricingCardProps = VariantProps<typeof createPricingCardVariants> & PricingCardCustomProps

export const PricingCard: FC<PricingCardProps> = props => {
  const isPrimary = props.size === 'primary'

  const style = createPricingCardVariants(props)
  const planTypeStyle = createLabelVariants({ ...props, size: 'primary' })
  const discountStyle = createLabelVariants({ size: 'small' })

  const headContentStyle = clsx('flex flex-col items-center gap-10', {
    'gap-16': isPrimary,
  })

  const headStyle = clsx('flex flex-col w-full gap-8', {
    'gap-12': isPrimary,
  })

  const planTypeContainer = (
    <div className={planTypeStyle}>
      <Text size='lg' thickness='medium'>
        {props.planType}
      </Text>
    </div>
  )

  const price_ = props.price ?? 'Free'
  const price = typeof price_ === 'number' ? `$${price_}` : price_
  const discount = typeof props.discount === 'number' ? `${props.discount}% Save` : props.discount

  const priceWrap = (
    <Title as='h2' thickness='bold' className='text-[3.5rem] leading-32'>
      {price}
    </Title>
  )

  const discountContainer = discount && (
    <div className={discountStyle}>
      <Text thickness='medium'>{discount}</Text>
    </div>
  )

  const priceContainer = (
    <div className='flex flex-row items-center gap-4'>
      {priceWrap}
      {discountContainer}
    </div>
  )

  const descriptionWrap = (
    <Text size='lg' thickness='medium'>
      {props.description}
    </Text>
  )

  const headContent = (
    <div className={headContentStyle}>
      {planTypeContainer}
      <div className='flex flex-col items-center'>
        {priceContainer}
        {descriptionWrap}
      </div>
    </div>
  )

  const head = (
    <div className={headStyle}>
      {headContent}
      <Separator
        decorative
        style={{
          height: '0.0625rem',
          width: '100%',
          backgroundColor: '#e8ebe2',
        }}
      />
    </div>
  )

  const features = props.features.map(feature => (
    <div key={feature} className='flex flex-row gap-6'>
      <CheckboxIcon width={24} height={24} className='shrink-0' />
      <Text size='lg' className='min-w-0 break-words'>
        {feature}
      </Text>
    </div>
  ))

  const featuresContainer = <div className='flex w-full flex-col gap-12'>{...features}</div>

  return (
    <div className={style}>
      {head}
      {featuresContainer}
      {props.button}
    </div>
  )
}
