'use client'

import { type CheckedState, Indicator, Root } from '@radix-ui/react-checkbox'
import { CheckboxIcon } from '@venture-vibe/icons'
import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC } from 'react'
import { Text } from './text'

const checkboxVariants = cva('flex items-center justify-center border transition-colors duration-200 ease-in-out', {
  variants: {
    variant: {
      primary: 'border-light-gray bg-white data-[state=checked]:bg-spring-bud data-[state=checked]:border-spring-bud',
      secondary: 'border-light-gray bg-white data-[state=checked]:bg-orange data-[state=checked]:border-orange',
    },
    size: {
      sm: 'size-8',
      md: 'size-10',
      lg: 'size-12',
    },
    roundness: {
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      max: 'rounded-max',
    },
  },
  defaultVariants: {
    variant: 'primary',
    size: 'md',
    roundness: 'md',
  },
})

interface CheckboxProps extends VariantProps<typeof checkboxVariants> {
  id?: string
  label?: string
  checked?: CheckedState
  defaultChecked?: CheckedState
  onCheckedChange?: ((checked: CheckedState) => void) | undefined
  className?: string
  labelClassName?: string
}

const Checkbox: FC<CheckboxProps> = ({
  id,
  label,
  variant,
  size,
  roundness,
  checked,
  defaultChecked,
  onCheckedChange,
  className,
  labelClassName,
}) => {
  return (
    <div className='flex items-center gap-4'>
      <Root
        id={id}
        className={clsx(checkboxVariants({ variant, size, roundness }), className)}
        {...(checked !== undefined ? { checked } : {})}
        {...(defaultChecked !== undefined ? { defaultChecked } : {})}
        {...(onCheckedChange ? { onCheckedChange } : {})}>
        <Indicator className='text-black'>
          <CheckboxIcon className='size-full' />
        </Indicator>
      </Root>
      {label && (
        <label htmlFor={id} className={clsx('select-none', labelClassName)}>
          <Text as='span' size='md'>
            {label}
          </Text>
        </label>
      )}
    </div>
  )
}

export { Checkbox, checkboxVariants, type CheckboxProps }
