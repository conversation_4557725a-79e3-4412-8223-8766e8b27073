'use client'

import { Indicator, Root } from '@radix-ui/react-progress'
import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC } from 'react'
import { useMemo } from 'react'
import { Text } from './text'

const FULL_PERCENTAGE = 100
const MIN_PERCENTAGE = 0

const progressVariants = cva(['relative overflow-hidden rounded-max w-full'], {
  variants: {
    color: {
      primary: ['bg-spring-stroke'],
      secondary: ['bg-light-orange'],
    },
    size: {
      sm: 'h-7.5',
      md: 'h-12.5',
      lg: 'h-17.5',
    },
  },
  defaultVariants: {
    color: 'primary',
    size: 'md',
  },
})

const progressIndicatorVariants = cva(
  ['size-full transition-transform duration-600 ease-[cubic-bezier(0.65,0,0.35,1)]'],
  {
    variants: {
      color: {
        primary: ['bg-spring-bud'],
        secondary: ['bg-orange'],
      },
    },
    defaultVariants: {
      color: 'primary',
    },
  },
)

const progressTextVariants = cva(['absolute flex items-center'], {
  variants: {
    indicatorLocation: {
      start: '-left-20',
      center: 'inset-0 justify-center',
      end: '-right-20',
    },
  },
  defaultVariants: {
    indicatorLocation: 'center',
  },
})

interface ProgressProps extends VariantProps<typeof progressVariants>, VariantProps<typeof progressTextVariants> {
  value?: number
  max?: number
  indicator?: boolean
  className?: string
}

const Progress: FC<ProgressProps> = ({
  value = 0,
  max = 100,
  color = 'primary',
  size = 'md',
  indicator = false,
  indicatorLocation = 'center',
  className,
  ...props
}) => {
  const percentage = useMemo(
    () => Math.min(Math.max((value / max) * FULL_PERCENTAGE, MIN_PERCENTAGE), FULL_PERCENTAGE),
    [value, max],
  )

  return (
    <div className={clsx('relative flex items-center', 'w-full')}>
      <Root
        className={progressVariants({ color, size, className })}
        style={{
          transform: 'translateZ(0)',
        }}
        value={value}
        max={max}
        {...props}>
        <Indicator
          className={progressIndicatorVariants({ color })}
          style={{ transform: `translateX(-${FULL_PERCENTAGE - percentage}%)` }}
        />
      </Root>
      {indicator && (
        <div className={progressTextVariants({ indicatorLocation })}>
          <Text as='span' thickness='medium' size='sm'>{`${percentage}%`}</Text>
        </div>
      )}
    </div>
  )
}

export { Progress, type ProgressProps, progressVariants, progressIndicatorVariants }
