'use client'
import { MagnifyingGlass } from '@venture-vibe/icons'
import { clsx } from '@venture-vibe/utils'
import { createContext, type FC, type ReactNode, useContext, useEffect, useMemo, useRef, useState } from 'react'
import { createPortal } from 'react-dom'
import { Input } from './input'
import { Link } from './link'
import { Text } from './text'
import { Title } from './title'

interface SearchResult {
  href: string
  title: string
}

interface SearchContextType {
  toggle: () => void
  links: SearchResult[]
}

const SearchContext = createContext<SearchContextType | null>(null)

const useGlobalSearch = () => {
  const context = useContext(SearchContext)
  if (!context) {
    throw new Error('useGlobalSearch must be used within GlobalSearchProvider')
  }
  return context
}

interface GlobalSearchProviderProps {
  children: ReactNode
  links: SearchResult[]
}

const GlobalSearchProvider: FC<GlobalSearchProviderProps> = ({ children, links }) => {
  const [isOpen, setIsOpen] = useState(false)
  const toggle = () => setIsOpen(!isOpen)

  return (
    <SearchContext.Provider value={{ toggle, links }}>
      {children}
      {isOpen && <GlobalSearch onClose={toggle} />}
    </SearchContext.Provider>
  )
}

interface GlobalSearchProps {
  placeholder?: string
  onClose: () => void
  className?: string
}

const GlobalSearch: FC<GlobalSearchProps> = ({ placeholder = 'Ara...', onClose, className }) => {
  const [query, setQuery] = useState('')
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const { links } = useGlobalSearch()
  const resultsRef = useRef<(HTMLButtonElement | null)[]>([])

  const filteredResults = useMemo(() => {
    if (!query.trim()) {
      return []
    }
    return links.filter(
      l => l.title.toLowerCase().includes(query.toLowerCase()) || l.href.toLowerCase().includes(query.toLowerCase()),
    )
  }, [query, links])

  useEffect(() => {
    if (filteredResults.length === 0) {
      setSelectedIndex(-1)
    } else if (selectedIndex >= filteredResults.length || selectedIndex === -1) {
      setSelectedIndex(0)
    }
  }, [filteredResults, selectedIndex])

  useEffect(() => {
    if (selectedIndex >= 0 && resultsRef.current[selectedIndex]) {
      resultsRef.current[selectedIndex]?.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth',
      })
    }
  }, [selectedIndex])

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
        return
      }
      if (filteredResults.length === 0) {
        return
      }
      if (e.key === 'ArrowDown') {
        e.preventDefault()
        setSelectedIndex(prev => {
          if (prev < filteredResults.length - 1) {
            return prev + 1
          }
          return 0
        })
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        setSelectedIndex(prev => {
          if (prev > 0) {
            return prev - 1
          }
          return filteredResults.length - 1
        })
      } else if (e.key === 'Enter' && selectedIndex >= 0) {
        e.preventDefault()
        const selectedResult = filteredResults[selectedIndex]
        if (selectedResult) {
          window.location.href = selectedResult.href
          onClose()
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onClose, filteredResults, selectedIndex])

  return createPortal(
    <div className='fixed inset-0 z-50 flex items-center justify-center pb-200'>
      <button type='button' className='absolute inset-0 animate-slideDownAndFade bg-black/60' onClick={onClose} />
      <div className={clsx('relative w-full max-w-250 animate-slideDownAndFade mx-8 sm:mx-0', className)}>
        <Input
          variant='primary'
          size='lg'
          roundness='2xl'
          value={query}
          icon={<MagnifyingGlass className='size-10 text-spanish-gray' />}
          onChange={e => setQuery(e.target.value)}
          placeholder={placeholder}
          className={clsx(
            'h-30 w-full px-8 border-none rounded-none',
            filteredResults.length > 0 || query.trim() !== '' ? 'rounded-t-2xl' : 'rounded-b-2xl rounded-t-2xl',
          )}
          autoFocus
        />
        {filteredResults.length > 0 && (
          <div
            className={clsx(
              'absolute right-0 left-0',
              'top-full z-10 max-h-200 border-t-light-gray border-t rounded-b-2xl overflow-y-auto',
              'animate-slideDownAndFade',
            )}>
            {filteredResults.map((result, index) => (
              <Link key={result.href} href={result.href}>
                <button
                  type='button'
                  ref={el => {
                    resultsRef.current[index] = el
                  }}
                  className={clsx(
                    'w-full cursor-pointer px-10 py-6 text-left duration-300',
                    selectedIndex === index ? 'bg-light-gray' : 'bg-white hover:bg-light-gray',
                    index === 0 && 'rounded-t-none',
                    index === filteredResults.length - 1 && 'rounded-b-2xl',
                  )}
                  onMouseEnter={() => setSelectedIndex(index)}
                  onClick={onClose}>
                  <Title thickness='normal' className='text-dark-gray'>
                    {result.title}
                  </Title>
                  <Text size='xs' className='text-dark-gray'>
                    {result.href}
                  </Text>
                </button>
              </Link>
            ))}
          </div>
        )}
        {query.trim() !== '' && filteredResults.length === 0 && (
          <div
            className={clsx(
              'absolute right-0 left-0',
              'top-full z-10 text-center bg-white border-t-light-gray border-t text-dark-gray',
              'px-10 py-8 rounded-b-2xl',
            )}>
            No Results
          </div>
        )}
      </div>
    </div>,
    document.body,
  )
}

interface GlobalSearchTriggerProps {
  placeholder?: string
  className?: string
}

const GlobalSearchTrigger: FC<GlobalSearchTriggerProps> = ({ placeholder = 'Search..', className }) => {
  const { toggle } = useGlobalSearch()
  return (
    <Input
      variant='tertiary'
      size='lg'
      roundness='max'
      onClick={toggle}
      icon={<MagnifyingGlass className='size-12 text-dark' />}
      className={clsx('px-8 w-full font-display text-lg', className)}
      placeholder={placeholder}
    />
  )
}

export {
  GlobalSearch,
  GlobalSearchTrigger,
  type GlobalSearchProps,
  type GlobalSearchTriggerProps,
  GlobalSearchProvider,
  useGlobalSearch,
  type SearchResult,
}
