import { cva, type VariantProps } from '@venture-vibe/utils'
import type { FC, ReactNode } from 'react'
import { Link } from './link'

const socialIconVariants = cva(
  ['rounded-full', 'flex items-center justify-center', 'duration-300', 'hover:cursor-pointer'],
  {
    variants: {
      color: {
        primary: 'bg-transparent text-white hover:bg-white hover:text-blue border border-white select-none',
        secondary: 'bg-dark hover:bg-orange text-white hover:text-black',
      },
      size: {
        sm: 'size-14',
        lg: 'size-16',
      },
    },
    defaultVariants: {
      color: 'primary',
      size: 'lg',
    },
  },
)

interface SocialIconProps extends VariantProps<typeof socialIconVariants> {
  href?: string
  className?: string
  icon: ReactNode
}

const SocialIcon: FC<SocialIconProps> = ({ className, icon, color, size, href = '#' }) => {
  return (
    <Link href={href} className={socialIconVariants({ color, size, className })}>
      {icon}
    </Link>
  )
}

export { SocialIcon, socialIconVariants, type SocialIconProps }
