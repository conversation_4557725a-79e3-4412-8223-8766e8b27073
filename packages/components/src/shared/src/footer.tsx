import { Separator } from '@radix-ui/react-separator'
import { clsx } from '@venture-vibe/utils'
import Image from 'next/image'
import type { FC, ReactNode } from 'react'
import { BackgroundSection } from './background-section'
import { Button } from './button'
import { Input } from './input'
import { Link } from './link'
import { Text } from './text'
import { Title } from './title'

interface FooterMenuItem {
  title: string
  href: string
}
interface FooterMenu {
  title: string
  menu: FooterMenuItem[]
}
interface FooterProps {
  iconUrl: string
  title: string
  description: string
  menuItems: FooterMenu[]
  bottomLeftText: ReactNode
  bottomRightText: ReactNode
  socialIcons?: ReactNode
  socialTitle?: string
  buttonText: string
  className?: string
}

const Footer: FC<FooterProps> = ({
  iconUrl,
  title,
  description,
  menuItems,
  bottomLeftText,
  bottomRightText,
  socialIcons,
  socialTitle,
  buttonText,
  className = '',
}) => {
  return (
    <footer className={clsx('bg-dark w-full', className)}>
      <BackgroundSection
        padding={null}
        layers={[
          {
            type: 'image',
            opacity: 0.3,
            image: '/grid.svg',
          },
        ]}
        className=''>
        <div className='container mx-auto px-20 md:px-40 lg:px-10 xl:px-10 w-full max-w-[82.5rem]'>
          <div className='flex flex-col lg:flex-row lg:justify-between pt-40 pb-20 gap-20'>
            {/* Left Side: Logo, Newsletter */}
            <div className='flex flex-col gap-32 lg:w-2/4'>
              <Image src={iconUrl} alt='Footer Logo' width={0} height={0} className='w-auto h-34 self-start' priority />
              <div className='flex flex-col gap-4'>
                <Title as='h6' size='xl' className='text-white'>
                  {title}
                </Title>
                <Text as='p' size='lg' className='text-white'>
                  {description}
                </Text>
              </div>
              <form className='flex flex-row gap-4 items-center'>
                <Input
                  type='email'
                  roundness='max'
                  variant='secondary'
                  required
                  name='Email'
                  placeholder='Enter your email'
                  className='px-6 py-3'
                />
                <Button type='submit' size={null} color='primary' className='rounded-max w-60 h-24 px-4 sm:w-60'>
                  {buttonText}
                </Button>
              </form>
            </div>
            <Separator
              decorative
              orientation='vertical'
              className='bg-dark-gray border-dark-gray mx-40 min-w-0 h-170 w-0 hidden desktop:block'
            />
            {/* Right Side: Menus and Social Media */}
            <div className='flex flex-col gap-20 md:gap-40 lg:w-2/4'>
              {/* Categories */}
              <div className='flex flex-col xs:flex-row gap-30 xs:gap-80'>
                {menuItems.map((menu, index) => (
                  <div key={String(index)} className='flex flex-col gap-4'>
                    <Title as='h6' size='2xl' thickness='medium' className='text-white'>
                      {menu.title}
                    </Title>
                    <div className='flex flex-col gap-2'>
                      {menu.menu.map((item, itemIndex) => (
                        <Link key={String(itemIndex)} href={item.href} className='text-white hover:underline'>
                          {item.title}
                        </Link>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              {/* Social Media Icons */}
              {socialIcons && (
                <div className='flex flex-col gap-4'>
                  <Text className='text-white font-semibold'>{socialTitle}</Text>
                  <div className='flex flex-row gap-3'>{socialIcons}</div>
                </div>
              )}
            </div>
          </div>

          {/* Bottom Section */}
          <div className='py-8'>
            <Separator className='bg-dark-gray h-px' />
            <div className='flex flex-col min-tablet:flex-row justify-between min-tablet:items-center pt-8 gap-8'>
              <Text className='text-[#bababa]'>{bottomLeftText}</Text>
              <Text className='text-[#bababa]'>{bottomRightText}</Text>
            </div>
          </div>
        </div>
      </BackgroundSection>
    </footer>
  )
}

export { Footer }
