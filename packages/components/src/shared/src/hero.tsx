'use client'

import {
  createContext,
  type Dispatch,
  type FC,
  type HTMLAttributes,
  type ReactNode,
  type SetStateAction,
  useContext,
  useEffect,
  useState,
} from 'react'

export interface HeroContextType {
  hasShadow: boolean
  setHasShadow: Dispatch<SetStateAction<boolean>>
}

export const HeroContext = createContext<HeroContextType | undefined>(undefined)

export function HeroProvider({ children }: { children: ReactNode }) {
  const [hasShadow, setHasShadow] = useState(false)

  const value = { hasShadow, setHasShadow }

  return <HeroContext.Provider value={value}>{children}</HeroContext.Provider>
}

export function useHero() {
  const context = useContext(HeroContext)

  if (context === undefined) {
    throw new Error('HeroContext')
  }

  return context
}

export type HeroProps = {
  as?: 'div' | 'section'
} & HTMLAttributes<HTMLDivElement> & { children?: ReactNode; className?: string }

export const Hero: FC<HeroProps> = ({ as = 'section', children, className, ...rest }) => {
  const El = as as 'div' | 'section'

  const { setHasShadow } = useHero()

  useEffect(() => {
    setHasShadow(true)

    return () => {
      setHasShadow(false)
    }
  }, [setHasShadow])

  return (
    <El className={className} {...rest}>
      {children}
    </El>
  )
}
