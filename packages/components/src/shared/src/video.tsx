import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC } from 'react'

const YOUTUBE_VIDEO_ID_REGEX = /(?:v=)([^&?]+)/

const videoVariants = cva(['w-full', 'h-full', 'rounded-lg'], {
  variants: {
    aspectRatio: {
      '16/9': 'aspect-video',
      '4/3': 'aspect-[4/3]',
      square: 'aspect-square',
    },
  },
  defaultVariants: {
    aspectRatio: '16/9',
  },
})

interface CaptionProps {
  src: string
  srcLang: string
  label: string
  kind?: 'subtitles' | 'captions' | 'descriptions' | 'chapters' | 'metadata'
}

type VideoProps = VariantProps<typeof videoVariants> & {
  className?: string
  title?: string
} & (
    | {
        type: 'hosted'
        src: string
        captions: CaptionProps[]
      }
    | {
        type: 'youtube' | 'vimeo'
        src: string
        captions?: never
      }
  )

const Video: FC<VideoProps> = ({ src, type, aspectRatio, className, title, ...props }) => {
  const getEmbedUrl = () => {
    if (type === 'youtube') {
      const videoIdMatch = src.match(YOUTUBE_VIDEO_ID_REGEX)
      const videoId = videoIdMatch ? videoIdMatch[1] : src.split('/').pop()
      return `https://www.youtube.com/embed/${videoId}`
    }
    if (type === 'vimeo') {
      const videoId = src.split('/').pop()
      return `https://player.vimeo.com/video/${videoId}`
    }
    return src
  }

  return (
    <div className={clsx(videoVariants({ aspectRatio }), className)}>
      {type === 'hosted' ? (
        // biome-ignore lint/a11y/useMediaCaption: Provide a track for captions when using audio or video elements
        <video controls className='w-full h-full rounded-lg'>
          <source src={src} type='video/mp4' />
          {'captions' in props &&
            props.captions?.map(caption => (
              <track
                key={caption.src}
                src={caption.src}
                kind={caption.kind || 'captions'}
                srcLang={caption.srcLang}
                label={caption.label}
              />
            ))}
          Your browser does not support the video tag.
        </video>
      ) : (
        <iframe
          src={getEmbedUrl()}
          title={title || 'Video Player'}
          allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
          allowFullScreen
          className='w-full h-full border-0 rounded-lg'
        />
      )}
    </div>
  )
}

export { Video, videoVariants, type VideoProps, type CaptionProps }
