'use client'

import { Loader } from '@venture-vibe/icons'
import { cva, type VariantProps } from '@venture-vibe/utils'
import clsx from 'clsx'
import type { FC, ReactNode } from 'react'
import { Link } from './link'

const buttonVariants = cva(
  [
    'rounded-max',
    'flex items-center justify-center font-medium',
    'transition-colors duration-300',
    'hover:cursor-pointer',
    'focus:outline-none',
    'gap-2',
  ],
  {
    variants: {
      color: {
        primary:
          'bg-spring-bud text-black hover:bg-black hover:text-white disabled:hover:bg-spring-bud disabled:hover:text-black',
        secondary: 'bg-orange text-white hover:bg-black disabled:hover:bg-orange disabled:hover:text-white',
        tertiary: 'bg-bright-blue text-white hover:bg-black disabled:hover:bg-bright-blue disabled:hover:text-white',
        outlineBlack:
          'border border-black text-black bg-transparent hover:bg-black hover:text-white disabled:hover:bg-transparent disabled:hover:text-black',
        outlineWhite:
          'border border-white text-black bg-transparent hover:bg-black hover:text-white disabled:hover:bg-transparent disabled:hover:text-black',
        error: 'bg-danger/20 text-danger',
        black: 'bg-black text-white',
        unstyled: '',
      },
      size: {
        lg: 'px-9 py-6 text-lg',
        sm: 'px-5 py-2.5 text-sm',
      },
    },
    defaultVariants: {
      color: 'primary',
      size: 'lg',
    },
  },
)

interface ButtonProps
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'color'>,
    VariantProps<typeof buttonVariants> {
  icon?: ReactNode
  iconPosition?: 'left' | 'right'
  iconClassName?: string
  loading?: boolean
  showLoadingText?: boolean
  href?: string
  target?: string
}

const Button: FC<ButtonProps> = ({
  children,
  icon,
  iconPosition = 'left',
  color,
  size,
  className,
  disabled,
  iconClassName,
  loading = false,
  showLoadingText = false,
  href,
  target,
  ...props
}) => {
  const isDisabled = disabled || loading

  const buttonClasses = clsx(
    buttonVariants({ color, size }),
    {
      'cursor-not-allowed opacity-75': isDisabled,
    },
    className,
  )

  const content = (
    <>
      {loading ? (
        <>
          <Loader className={clsx('flex-shrink-0 animate-spin', iconClassName)} />
          {showLoadingText && children && <span>{children}</span>}
        </>
      ) : (
        <>
          {icon && iconPosition === 'left' && <span className={clsx('flex-shrink-0', iconClassName)}>{icon}</span>}
          {children && <span>{children}</span>}
          {icon && iconPosition === 'right' && <span className={clsx('flex-shrink-0', iconClassName)}>{icon}</span>}
        </>
      )}
    </>
  )

  if (href) {
    return (
      <Link href={href} openInNewTab={target === '_blank'} className={buttonClasses}>
        {content}
      </Link>
    )
  }

  return (
    <button type={props.type || 'button'} disabled={isDisabled} className={buttonClasses} {...props}>
      {content}
    </button>
  )
}

export { Button, buttonVariants }
