import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC, ReactNode } from 'react'
import { Text } from './text'

const badgeVariants = cva(['rounded-max', 'p-4', 'flex flex-row items-center gap-2'], {
  variants: {
    variant: {
      primary: 'bg-light-spring text-black',
      'primary-accent': 'bg-spring-bud text-dark-gray',
      'primary-border': 'border border-spring-bud bg-transparent text-spring-bud',
      secondary: 'bg-light-orange text-black',
      'secondary-accent': 'bg-orange text-white',
      'secondary-border': 'border border-orange bg-transparent text-orange',
      tertiary: 'bg-light-orange text-black',
      'tertiary-accent': 'bg-orange text-white',
      'tertiary-border': 'border border-orange bg-transparent text-orange',
      quaternary: 'bg-white text-black border border-platinum-stroke',
      'quaternary-accent': 'bg-dark-gray text-white',
      'quaternary-border': 'border border-dark-gray bg-transparent text-black',
      danger: 'bg-danger text-white',
      blue: 'bg-bright-blue text-white',
    },
    size: {
      xs: 'h-12 text-2xs',
      sm: 'h-14 text-2xs',
      md: 'h-16 text-xs',
      lg: 'h-18 text-sm',
    },
    roundness: {
      xs: 'rounded-xs',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      xl: 'rounded-xl',
      xxl: 'rounded-2xl',
      max: 'rounded-max',
    },
  },
  defaultVariants: {
    variant: 'primary',
    size: 'md',
    roundness: 'max',
  },
})

interface BadgeProps extends VariantProps<typeof badgeVariants> {
  text: string
  leftIcon?: ReactNode
  rightIcon?: ReactNode
  className?: string
}
const Badge: FC<BadgeProps> = ({ text, leftIcon, rightIcon, variant, size = 'md', roundness, className }) => {
  return (
    <span className={clsx(badgeVariants({ variant, size, roundness }), className)}>
      {leftIcon && <span>{leftIcon}</span>}
      <Text size={size}>{text}</Text>
      {rightIcon && <span>{rightIcon}</span>}
    </span>
  )
}
export { Badge, badgeVariants, type BadgeProps }
