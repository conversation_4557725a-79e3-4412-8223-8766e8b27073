'use client'
import { ArrowRight } from '@venture-vibe/icons'
import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC, ReactNode } from 'react'
import { useMemo } from 'react'
import { Button } from './button'
import { Link } from './link'
import { Title } from './title'

const FIRST_ROW_IMAGE_COUNT = 3
const SECOND_ROW_START_INDEX = 3
const TOTAL_ITEM = 5

const categoryCardVariants = cva(
  ['rounded-3xl', 'px-8 md:px-12', 'py-6 md:py-8', 'font-display', 'w-full', 'max-w-full', 'md:max-w-310'],
  {
    variants: {
      color: {
        primary: ['bg-light-spring border border-spring-stroke'],
        secondary: ['bg-light-orange border border-orange-stroke'],
      },
    },
    defaultVariants: {
      color: 'primary',
    },
  },
)

const containerVariants = clsx([
  'flex flex-row',
  'items-start',
  'sm:items-center',
  'justify-between',
  'mb-6',
  'sm:mb-8',
  'md:mb-10',
  'mt-2',
  'gap-6',
])

const imageVariants = clsx(['w-full', 'rounded-xl', 'bg-cover', 'bg-top', 'bg-no-repeat', 'h-40', 'md:h-70'])

type BaseProps = VariantProps<typeof categoryCardVariants> & {
  title: string
  buttonText: string
  link: string
  className?: string
}

type CategoryCardProps =
  | (BaseProps & { customContent?: true; content?: ReactNode; imageUrl?: never })
  | (BaseProps & { customContent?: false; content?: never; imageUrl: string[] })

const CategoryCard: FC<CategoryCardProps> = ({
  title,
  imageUrl = [],
  buttonText,
  customContent,
  content,
  color,
  className,
  ...props
}) => {
  const { firstRowImages, secondRowImages } = useMemo(
    () => ({
      firstRowImages: imageUrl.slice(0, FIRST_ROW_IMAGE_COUNT),
      secondRowImages: imageUrl.slice(SECOND_ROW_START_INDEX, TOTAL_ITEM),
    }),
    [imageUrl],
  )

  return (
    <article className={categoryCardVariants({ color })} {...props}>
      <div className={containerVariants}>
        <Title as='h6' size='xl' thickness='medium' className='text-dark'>
          {title}
        </Title>
        <Link href={props.link}>
          <Button
            color={color}
            size='lg'
            className='text-md group px-6 py-3 md:px-10 md:py-4 h-22'
            icon={<ArrowRight className='-rotate-45 group-hover:rotate-0 duration-300' />}
            iconPosition='right'>
            {buttonText}
          </Button>
        </Link>
      </div>
      <div className={clsx('space-y-4 sm:space-y-6 md:space-y-8')}>
        {customContent && <div className={clsx('w-full overflow-hidden rounded-3xl', 'h-110 md:h-162')}>{content}</div>}
        {!customContent && (
          <>
            <div className='grid grid-cols-3 gap-2 sm:gap-3 md:gap-5'>
              {firstRowImages.map(url => (
                <div key={url} className={imageVariants} style={{ backgroundImage: `url(${url})` }} />
              ))}
            </div>
            {imageUrl.length > FIRST_ROW_IMAGE_COUNT && (
              <div className='grid grid-cols-2 gap-2 sm:gap-3 md:gap-5'>
                {secondRowImages.map(url => (
                  <div key={url} className={imageVariants} style={{ backgroundImage: `url(${url})` }} />
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </article>
  )
}

export { CategoryCard, categoryCardVariants, type CategoryCardProps }
