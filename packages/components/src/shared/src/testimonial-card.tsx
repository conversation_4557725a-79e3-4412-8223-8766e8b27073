'use client'

import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC, ImgHTMLAttributes, ReactElement } from 'react'
import { Text } from './text'

const paragraphVariants = clsx(
  'leading-[1.75rem] tablet:leading-[2.25rem] ',
  'tracking-[-0.36] tablet:tracking-[-0.32] ',
  'text-md tablet:text-lg',
)

const contentVariants = clsx(
  'flex',
  'flex-col',
  'text-left',
  'normal-case',
  'select-none',
  'break-words',
  'gap-y-[1.75rem] tablet:gap-y-[2rem]',
)

export const testimonialCardVariants = cva(
  [
    'flex',
    'flex-col',
    'box-border',
    'border-solid',
    'border-[1px]',
    'gap-y-12 tablet:gap-y-20 px-12 tablet:px-36 pt-12 tablet:pt-28 pb-10 tablet:pb-20 max-w-[55rem] tablet:max-w-[80rem] rounded-[1.25rem] tablet:rounded-[2.5rem]',
  ],
  {
    variants: {
      color: {
        default: ['bg-dark', 'text-white', 'border-dark-gray'],
      },
      font: {
        default: ['font-display'],
      },
    },
    defaultVariants: {
      color: 'default',
      font: 'default',
    },
  },
)

export interface TestimonialCardCustomProps {
  logo?: ReactElement<ImgHTMLAttributes<HTMLImageElement>>
  name: string
  position: string
}

export type TestimonialCardProps = React.HTMLAttributes<HTMLDivElement> &
  VariantProps<typeof testimonialCardVariants> &
  TestimonialCardCustomProps

export const TestimonialCard: FC<TestimonialCardProps> = props => {
  const { children } = props

  const paragraph = (
    <Text thickness='normal' size={null} className={paragraphVariants}>
      {children}
    </Text>
  )

  const name = (
    <Text as='span' thickness='medium' size={null} className='text-lg tablet:text-2xl'>
      {props.name}
    </Text>
  )

  const position = (
    <Text as='span' size={null} className='text-sm tablet:text-md'>
      {props.position}
    </Text>
  )

  const content = (
    <>
      <div>{paragraph}</div>
      <div className='flex flex-col gap-y-[0.5rem]'>
        {name}
        {position}
      </div>
    </>
  )

  return (
    <div className={testimonialCardVariants(props)}>
      {props.logo && <div>{props.logo}</div>}
      <div className={contentVariants}>{content}</div>
    </div>
  )
}
