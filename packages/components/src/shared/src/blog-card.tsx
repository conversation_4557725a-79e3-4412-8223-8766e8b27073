import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import { type FC, memo, type ReactNode } from 'react'
import { Link } from './link'
import { Text } from './text'
import { Title } from './title'

const blogCardVariants = cva(['rounded-max', 'flex', 'font-display', 'group', 'hover:cursor-pointer'], {
  variants: {
    variant: {
      primary: 'bg-white',
      secondary: 'bg-light-spring',
      tertiary: 'bg-light-orange',
      quaternary: 'bg-transparent',
    },
  },
  defaultVariants: {
    variant: 'primary',
  },
  // compoundVariants: [
  //   { type: 'primary', class: 'max-w-195 md:max-w-325 lg:max-w-640' },
  //   { type: 'secondary', class: 'max-w-160 lg:max-w-206' },
  // ],
})

type BaseProps = Omit<VariantProps<typeof blogCardVariants>, 'type'> & {
  image: string
  title: string
  description: string
  link?: string
  className?: string
}

type BlogCardProps =
  | (BaseProps & { type: 'primary'; date?: string; badge?: ReactNode })
  | (BaseProps & { type: 'secondary'; date?: never; badge?: never })

const BlogCard: FC<BlogCardProps> = memo(
  ({ type, image, title, description, link, badge, date, className, variant }) => {
    return (
      <Link href={link ? link : '#'} className={clsx('w-full', 'flex justify-center')}>
        {type === 'primary' && (
          <div
            className={clsx(
              blogCardVariants({ variant }),
              className,
              'max-w-195 md:max-w-235 tablet:max-w-640 border border-platinum-stroke flex-col tablet:flex-row',
            )}>
            <div className={clsx('overflow-hidden', 'rounded-3xl', 'm-6', 'tablet:w-full')}>
              <div
                className={clsx(
                  'h-160 md:h-200 tablet:h-220 lg:h-240',
                  'rounded-3xl',
                  'bg-cover',
                  'bg-center',
                  'transition-transform',
                  'duration-800',
                  'group-hover:scale-110',
                )}
                style={{ backgroundImage: `url(${image})` }}
              />
            </div>
            <div
              className={clsx(
                'flex flex-col items-start justify-center',
                'w-full',
                'space-y-12',
                'p-15 tablet:pr-90 tablet:pl-30',
              )}>
              <div className={clsx('space-x-4')}>{badge}</div>
              <div className={clsx('flex flex-col', 'space-y-4')}>
                <Title as='h5' thickness='bold' size='2xl'>
                  {title}
                </Title>
                <Text as='span' thickness='medium' size='md' className='text-dark-gray'>
                  {date}
                </Text>
                <Text as='p' thickness='normal' size='md' className={clsx('text-dark-gray', 'text-justify')}>
                  {description}
                </Text>
              </div>
            </div>
          </div>
        )}
        {type === 'secondary' && (
          <div className={clsx(blogCardVariants({ variant }), className, 'flex-col')}>
            <div className={clsx('overflow-hidden', 'rounded-2xl')}>
              <div
                className={clsx(
                  'h-125 lg:h-140',
                  'rounded-t-2xl',
                  'bg-cover',
                  'bg-center',
                  'transition-transform',
                  'duration-500',
                  'group-hover:scale-110',
                )}
                style={{ backgroundImage: `url(${image})` }}
              />
            </div>
            <div className={clsx('p-6', 'flex flex-col')}>
              <div className={clsx('space-y-2', 'text-left')}>
                <div className={clsx('flex items-center justify-between')}>
                  <Title as='h6' thickness='semibold' size='lg'>
                    {title}
                  </Title>
                </div>
                <Text as='p' thickness='normal' size='sm' className={clsx('text-dark-gray')}>
                  {description}
                </Text>
              </div>
            </div>
          </div>
        )}
      </Link>
    )
  },
)

export { BlogCard, type BlogCardProps, blogCardVariants }
