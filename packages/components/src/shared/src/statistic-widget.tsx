'use client'

import { Separator } from '@radix-ui/react-separator'
import { cva, type VariantProps } from '@venture-vibe/utils'
import type { FC, HTMLAttributes, ImgHTMLAttributes, ReactElement } from 'react'
import { Text } from './text'
import { Title } from './title'

const createStatisticWidgetVariants = cva(
  [
    'flex',
    'flex-col',
    'gap-y-8',
    'font-display',
    'lg:max-w-[370px]',
    'md:max-w-[455px]',
    'max-md:max-w-[580px]',
    'min-w-fit',
  ],
  {
    variants: {
      color: {
        default: 'text-[#101703]',
      },
    },
    defaultVariants: {
      color: 'default',
    },
  },
)

export interface StatisticWidgetCustomProps {
  icon: ReactElement<ImgHTMLAttributes<HTMLImageElement>>
  title: string
}

export type StatisticWidgetProps = HTMLAttributes<HTMLElement> &
  VariantProps<typeof createStatisticWidgetVariants> &
  StatisticWidgetCustomProps

export const StatisticWidget: FC<StatisticWidgetProps> = ({ children, title, icon, ...props }) => {
  const titleContainer = (
    <div className='flex items-center gap-8'>
      {icon}
      <Title
        size={null}
        thickness='bold'
        className='lg:text-[1.5rem] lg:leading-[2rem] lg:tracking-[-0.36px] md:text-[1.375rem] md:leading-[1.875rem] md:tracking-[-0.36px] max-md:text-[1.25rem] max-md:leading-[1.65rem] max-md:tracking-[-0.32px]'>
        {title}
      </Title>
    </div>
  )

  const textSeparator = (
    <Separator
      decorative
      orientation='vertical'
      style={{
        width: '0.120rem',
        backgroundColor: '#e8ebe2',
        marginLeft: '0.875rem',
      }}
      className='max-lg:hidden'
    />
  )

  const textContainer = (
    <div className='flex w-full justify-between gap-x-18'>
      {textSeparator}
      <Text className='lg:w-[320px] lg:text-[18px] lg:leading-[32px] lg:tracking-[-0.36px] md:text-[18px] md:leading-[32px] md:tracking-[-0.36px] max-md:text-[16px] max-md:leading-[28px] max-md:tracking-[-0.32px]'>
        {children}
      </Text>
    </div>
  )

  return (
    <div className={createStatisticWidgetVariants(props)}>
      {titleContainer}
      {textContainer}
    </div>
  )
}
