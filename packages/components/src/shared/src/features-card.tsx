'use client'

import { ArrowRight } from '@venture-vibe/icons'
import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC, ReactNode } from 'react'
import { Button } from './button'
import { Text } from './text'
import { Title } from './title'

const createTitleVariants = cva(['font-display', 'font-black'], {
  variants: {
    size: {
      lg: ['text-[2rem]'],
      md: ['text-[1.75rem]'],
      sm: ['text-[1.25rem]'],
    },
  },
  defaultVariants: {
    size: 'md',
  },
})

const createTextVariants = cva(['font-display', 'text-dark-gray'], {
  variants: {
    size: {
      lg: ['max-w-[24.75rem]'],
      md: ['max-w-[23.75rem]'],
      sm: ['max-w-[23.75rem]', 'leading-14'],
    },
  },
  defaultVariants: {
    size: 'md',
  },
})

export const createFeaturesCardVariants = cva(
  [
    'flex',
    'flex-col',
    'relative',
    'rounded-3xl',
    'min-w-min',
    'min-h-min',
    'overflow-hidden',
    'gap-[1.75rem]',
    '[&::after]:content-[""]',
    '[&::after]:block',
    '[&::after]:absolute',
    '[&::after]:inset-0',
    '[&::after]:w-full',
    '[&::after]:h-full',
    '[&::after]:border-[0.8px]',
    '[&::after]:border-solid',
    '[&::after]:pointer-events-none',
    '[&::after]:box-border',
    '[&::after]:rounded-3xl',
  ],
  {
    variants: {
      color: {
        primary: ['bg-[#F1F9E7]', '[&::after]:border-[#DDE8C6]'],
        secondary: ['bg-[#FFF2EB]', '[&::after]:border-[#F4E2D9]'],
      },
      size: {
        lg: ['max-w-[39.25rem]', 'min-w-[35rem]', 'p-20', 'pt-16'],
        md: ['max-w-[29.0625rem]', 'min-w-[22.825rem]', 'px-12', 'py-20'],
        sm: ['max-w-[21.625rem]', 'px-12', 'py-20'],
      },
    },
    defaultVariants: {
      color: 'primary',
      size: 'md',
    },
  },
)

export type FeatureCardVariantProps = VariantProps<typeof createFeaturesCardVariants>

export interface FeaturesCardButtonInfo {
  text: string
  href: string
}

export interface FeaturesCardCustomProps {
  title: string
  text: string
  buttonInfo: FeaturesCardButtonInfo
}

export type FeaturesCardProps = FeatureCardVariantProps &
  FeaturesCardCustomProps &
  (
    | { attachment?: never }
    | {
        size: 'lg'
        attachment?: ReactNode
      }
  ) & {
    className?: string
  }

export const FeaturesCard: FC<FeaturesCardProps> = props => {
  const { size, attachment, className } = props

  const titleStyle = createTitleVariants(props)
  const textStyle = createTextVariants(props)

  const contentContainerStyle = clsx('flex', 'flex-col', 'gap-8', size === 'lg' && 'gap-12')

  const style = clsx(createFeaturesCardVariants(props), attachment && 'gap-[9.75rem]', className)

  const title = (
    <Title as='h5' className={titleStyle}>
      {props.title}
    </Title>
  )

  const text = (
    <Text size='lg' className={textStyle}>
      {props.text}
    </Text>
  )

  const contentContainer = (
    <div className={contentContainerStyle}>
      {title}
      {text}
      {attachment}
    </div>
  )

  const button = (
    <Button
      color='outlineBlack'
      icon={<ArrowRight />}
      iconPosition='right'
      className='group hover:cursor-pointer'
      iconClassName='transition-transform duration-300 -rotate-45 group-hover:rotate-0'>
      <a href={props.buttonInfo.href}>
        <Text>{props.buttonInfo.text}</Text>
      </a>
    </Button>
  )

  return (
    <div className={style}>
      {contentContainer}
      <div>{button}</div>
    </div>
  )
}
