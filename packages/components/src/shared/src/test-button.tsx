'use client'

import { cva, type VariantProps } from '@venture-vibe/utils'
import { type FC, useState } from 'react'

export const testButtonVariants = cva([], {
  variants: {
    color: {
      primary: ['bg-[#36a2eb]'],
      secondary: ['bg-[#ff6384]'],
    },

    size: {
      default: 'px-4 py-2',
    },
  },
  defaultVariants: {
    color: 'primary',
    size: 'default',
  },
})

export const TestButton: FC<
  React.ButtonHTMLAttributes<HTMLButtonElement> & VariantProps<typeof testButtonVariants>
> = ({ children, ...props }) => {
  const state = useState(true)

  return (
    <button type='button' className={testButtonVariants(props)}>
      {children}
    </button>
  )
}
