'use client'

import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC, ReactNode } from 'react'
import { type IntersectionOptions, useInView } from 'react-intersection-observer'

/**
 * Temporary solution
 */
const animations = {
  up: {
    true: 'animate-appearUpLate',
    false: 'animate-appearUp',
  },
  right: {
    true: 'animate-appearRightLate',
    false: 'animate-appearRight',
  },
  left: {
    true: 'animate-appearLeftLate',
    false: 'animate-appearLeft',
  },
}

export const createAppearVariants = cva([], {
  variants: {},
})

export interface AppearCustomProps {
  as?: 'div' | 'section'
  direction?: 'up' | 'right' | 'left'
  late?: boolean
  loop?: boolean
  options?: IntersectionOptions
}

export type AppearProps = VariantProps<typeof createAppearVariants> &
  AppearCustomProps & {
    children?: ReactNode
    className?: string
  }

export const Appear: FC<AppearProps> = ({
  as: El = 'div',
  direction = 'up',
  late = false,
  className,
  options,
  ...props
}) => {
  const { ref, inView } = useInView({
    triggerOnce: !props.loop,
    threshold: 0.1,
    ...options,
  })

  return (
    <El ref={ref} className={clsx('w-fit h-fit opacity-0', inView && animations[direction][`${late}`], className)}>
      {props.children}
    </El>
  )
}
