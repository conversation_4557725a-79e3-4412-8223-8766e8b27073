'use client'

import { ArrowRight } from '@venture-vibe/icons'
import { type BlobType, clsx } from '@venture-vibe/utils'
import type { FC, ReactNode } from 'react'
import React, { cloneElement, createContext, isValidElement, useCallback, useContext, useEffect, useState } from 'react'
import { Button } from './button'

interface ClassNameProp {
  className?: string
}

// --- Context ---
interface CarouselContextType {
  currentIndex: number
  slides: ReactNode[]
  scrollNext: () => void
  scrollPrev: () => void
  scrollTo: (index: number) => void
}

const CarouselContext = createContext<CarouselContextType | null>(null)

const useCarousel = () => {
  const context = useContext(CarouselContext)
  if (!context) {
    throw new Error('useCarousel must be used within a CarouselProvider')
  }
  return context
}

// --- CarouselDots Component ---
interface CarouselDotsProps {
  variant?: 'dots' | 'preview' | undefined
  className?: string | undefined
  previewClassName?: string | undefined
}

const CarouselDots: FC<CarouselDotsProps> = ({ variant = 'dots', className, previewClassName }) => {
  const { currentIndex, slides, scrollTo } = useCarousel()

  return (
    <div className={clsx('flex items-center justify-center mt-4', className)}>
      {slides.map((slide, index) =>
        variant === 'dots' ? (
          <button
            key={Number(index)}
            type='button'
            onClick={() => scrollTo(index)}
            className={clsx(
              'w-3 h-3 rounded-full transition-colors',
              currentIndex === index ? 'bg-black' : 'bg-gray hover:bg-gray-500',
            )}
            aria-label={`Go to slide ${index + 1}`}
          />
        ) : (
          <button
            key={Number(index)}
            type='button'
            onClick={() => scrollTo(index)}
            className={clsx(
              'w-full aspect-video m-2 overflow-hidden rounded-md border-2 transition-all',
              currentIndex === index ? 'border-black scale-105' : 'border-gray opacity-70 hover:opacity-100',
              previewClassName,
            )}>
            <div className='w-full h-full pointer-events-none'>
              {isValidElement<ClassNameProp>(slide) && slide.type === 'img'
                ? cloneElement(slide, { className: 'w-full h-full object-cover' })
                : null}
            </div>
          </button>
        ),
      )}
    </div>
  )
}
CarouselDots.displayName = 'CarouselDots'

// --- Carousel Component ---
interface CarouselProps {
  children: ReactNode
  loop?: boolean
  autoplay?: boolean
  autoplayDelay?: number
  showArrows?: boolean
  className?: string

  // 🔽 yeni eklenen dots prop'ları
  showDots?: boolean
  dotsVariant?: 'dots' | 'preview'
  dotsClassName?: string
  dotsPreviewClassName?: string
}

const Carousel: FC<CarouselProps> = ({
  children,
  loop = false,
  autoplay = false,
  autoplayDelay = 3000,
  showArrows = true,
  className,
  showDots = true,
  dotsVariant = 'dots',
  dotsClassName,
  dotsPreviewClassName,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const childrenArray = React.Children.toArray(children)
  const slides = childrenArray.filter(
    child => isValidElement(child) && (child.type as BlobType).displayName !== 'CarouselDots',
  )

  const scrollPrev = useCallback(() => {
    setCurrentIndex(prevIndex => {
      if (prevIndex === 0) {
        return loop ? slides.length - 1 : 0
      }
      return prevIndex - 1
    })
  }, [loop, slides.length])

  const scrollNext = useCallback(() => {
    setCurrentIndex(prevIndex => {
      if (prevIndex === slides.length - 1) {
        return loop ? 0 : prevIndex
      }
      return prevIndex + 1
    })
  }, [loop, slides.length])

  const scrollTo = (index: number) => {
    if (index >= 0 && index < slides.length) {
      setCurrentIndex(index)
    }
  }

  // autoplay
  useEffect(() => {
    if (!autoplay) {
      return
    }
    const timer = setInterval(scrollNext, autoplayDelay)
    return () => clearInterval(timer)
  }, [autoplay, autoplayDelay, scrollNext])

  const contextValue = {
    currentIndex,
    slides,
    scrollPrev,
    scrollNext,
    scrollTo,
  }
  const a = 100

  return (
    <CarouselContext.Provider value={contextValue}>
      <div className={clsx('relative w-full', className)}>
        <div className='overflow-hidden'>
          <div
            className='flex transition-transform duration-500 ease-in-out'
            style={{ transform: `translateX(-${currentIndex * a}%)` }}>
            {slides.map((slide, index = 0) => (
              <div key={Number(index)} className='flex-shrink-0 w-full h-[30rem]'>
                {slide}
              </div>
            ))}
          </div>
        </div>

        {showArrows && (
          <>
            <Button
              size={null}
              color='unstyled'
              onClick={scrollPrev}
              className={clsx(
                'absolute -translate-y-1/2 left-4 z-10 cursor-pointer hover:bg-gray/50',
                'w-20 h-20 rounded-full bg-white text-black',
                slides.length > 2 ? 'top-1/3' : 'top-1/2',
              )}
              disabled={!loop && currentIndex === 0}>
              <ArrowRight className='transform rotate-180' />
            </Button>
            <Button
              size={null}
              color='unstyled'
              onClick={scrollNext}
              className={clsx(
                'absolute -translate-y-1/2 right-4 z-10 cursor-pointer hover:bg-gray/50',
                'w-20 h-20 rounded-full bg-white text-black',
                slides.length > 2 ? 'top-1/3' : 'top-1/2',
              )}
              disabled={!loop && currentIndex === slides.length - 1}>
              <ArrowRight />
            </Button>
          </>
        )}

        {showDots && slides.length > 2 && (
          <CarouselDots
            variant={dotsVariant}
            {...(dotsClassName ? { className: dotsClassName } : {})}
            {...(dotsPreviewClassName ? { previewClassName: dotsPreviewClassName } : {})}
          />
        )}
      </div>
    </CarouselContext.Provider>
  )
}

export { Carousel, CarouselDots, type CarouselProps, type CarouselDotsProps }
