import { Fallback as AvatarFallback, Image as AvatarImage, Root as AvatarRoot } from '@radix-ui/react-avatar'
import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import { type FC, useMemo } from 'react'

const avatarVariants = cva('inline-flex items-center justify-center overflow-hidden align-middle select-none', {
  variants: {
    color: {
      default: 'bg-light-gray text-dark-gray',
      spring: 'bg-dark-spring text-light-spring',
      'spring-light': 'bg-light-spring text-dark-spring',
      orange: 'bg-orange text-white',
      'orange-light': 'bg-light-orange text-orange',
      'orange-bright': 'bg-bright-orange text-white',
      blue: 'bg-bright-blue text-white',
      red: 'bg-danger text-white',
      gray: 'bg-gray text-white',
      'gray-dark': 'bg-dark-gray text-white',
      'gray-spanish': 'bg-spanish-gray text-white',
    },
    size: {
      sm: 'w-10 h-10',
      md: 'w-20 h-20',
      lg: 'w-30 h-30',
      xl: 'w-40 h-40',
      '2xl': 'w-50 h-50',
    },
    roundness: {
      xs: 'rounded-xs',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      xl: 'rounded-xl',
      '2xl': 'rounded-2xl',
      max: 'rounded-max',
      full: 'rounded-full',
    },
  },
  defaultVariants: {
    color: 'default',
    size: 'md',
    roundness: 'full',
  },
})

interface AvatarProps extends VariantProps<typeof avatarVariants> {
  image?: string
  alt?: string
  fallback: string
  className?: string
}

interface AvatarGroupProps extends VariantProps<typeof avatarVariants> {
  data: AvatarProps[]
  className?: string
}

const Avatar: FC<AvatarProps> = ({ image, alt, fallback, className, color, size, roundness }) => {
  return (
    <AvatarRoot>
      <AvatarImage className={clsx(avatarVariants({ color, size, roundness }), className)} src={image} alt={alt} />
      <AvatarFallback className={clsx('flex items-center justify-center', avatarVariants({ color, size, roundness }))}>
        {fallback}
      </AvatarFallback>
    </AvatarRoot>
  )
}

const AvatarGroup: FC<AvatarGroupProps> = ({ data, color, size, roundness, className }) => {
  const spacing = {
    sm: '-space-x-3',
    md: '-space-x-6',
    lg: '-space-x-8',
    xl: '-space-x-10',
    '2xl': '-space-x-12',
  }

  const avatarElements = useMemo(
    () =>
      data.map(avatar => (
        <AvatarRoot
          key={avatar.image || avatar.fallback}
          className={clsx(
            size === 'sm' ? 'border-1' : 'border-2',
            'border-white',
            avatarVariants({ color: avatar.color ? avatar.color : color, size, roundness }),
          )}>
          <AvatarImage
            className={clsx(avatarVariants({ color: avatar.color ? avatar.color : color, size, roundness }), className)}
            src={avatar.image}
            alt={avatar.alt}
          />
          <AvatarFallback
            className={clsx(
              'flex items-center justify-center',
              avatarVariants({ color: avatar.color ? avatar.color : color, size, roundness }),
              size === 'sm' ? 'text-2xs' : size === '2xl' ? 'text-2xl' : '',
            )}>
            {avatar.fallback}
          </AvatarFallback>
        </AvatarRoot>
      )),
    [data, color, size, roundness, className],
  )

  return <div className={clsx('flex', spacing[size || 'md'])}>{avatarElements}</div>
}

export { Avatar, AvatarGroup, type AvatarProps, type AvatarGroupProps, avatarVariants }
