'use client'

import { type BlobType, clsx } from '@venture-vibe/utils'
import {
  type ClassAttributes,
  type Dispatch,
  type FC,
  type HTMLAttributes,
  type ReactNode,
  type SetStateAction,
  useEffect,
  useId,
  useState,
} from 'react'

/** biome-ignore lint/nursery/noUnresolvedImports: Package's fault */
import ReactMarkdown, { type ExtraProps } from 'react-markdown'
import rehypeHighlight from 'rehype-highlight'
import rehypeRaw from 'rehype-raw'
import rehypeSanitize from 'rehype-sanitize'
import remarkGfm from 'remark-gfm'

export const MarkdownTitleTypes = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'] as const

export type MarkdownTitleType = (typeof MarkdownTitleTypes)[number]
export type MarkdownTitleProps = ClassAttributes<HTMLHeadingElement> & HTMLAttributes<HTMLHeadingElement> & ExtraProps
export type MarkdownTitleHandler = (props: MarkdownTitleProps) => ReactNode
export type MarkdownTitleProxy = {
  [Key in MarkdownTitleType]: MarkdownTitleHandler
}
export type MarkdownTitlesDispath = Dispatch<SetStateAction<MarkdownTitle[]>>

export interface MarkdownTitle {
  id: string
  type: MarkdownTitleType
  props: MarkdownTitleProps
}

export interface MarkdownNestedNode {
  self: MarkdownTitle
  children: MarkdownNestedNodeChild[]
}

export type MarkdownNestedNodeChild = MarkdownNestedNode | MarkdownTitle

export interface MarkdownProps {
  source: string
  className?: string
  titlesDispatch?: MarkdownTitlesDispath
}

/**
 * The component uses this proxy to capture titles,
 * another packages like unified or it's plugins could be used but
 * that would add unnecessary complexity to the component,
 * so this simple solution is enough.
 */
export const createTitleProxy = (setTitles: MarkdownTitlesDispath) => {
  const proxy = MarkdownTitleTypes.reduce((acc, v) => {
    const Tag = v as BlobType

    const handler: MarkdownTitleHandler = props => {
      const id = useId()

      // biome-ignore lint/correctness/useExhaustiveDependencies: Once
      useEffect(() => {
        /**
         * Schedule to after the current macro,
         * for preventing infinite render loop.
         */
        setTimeout(() => setTitles(previous => [...previous, { id, type: v, props }]))
      }, [])

      return <Tag id={id} {...props} />
    }

    acc[v] = handler
    return acc
  }, {} as MarkdownTitleProxy)

  return proxy
}

// biome-ignore lint/nursery/useExportsLast: Reduntant
export const isMarkdownNestedNode = (node: MarkdownNestedNodeChild): node is MarkdownNestedNode => {
  return Object.hasOwn(node, 'self')
}

const placeInNested = (nested: MarkdownNestedNodeChild[], title: MarkdownTitle) => {
  const last = nested.at(-1)

  if (last === undefined) {
    nested.push(title)
    return
  }

  if (isMarkdownNestedNode(last)) {
    if (title.type <= last.self.type) {
      nested.push(title)
    } else {
      placeInNested(last.children, title)
    }

    return
  }

  if (title.type <= last.type) {
    nested.push(title)
    return
  }

  const node: MarkdownNestedNode = {
    self: last,
    children: [title],
  }

  nested.pop()
  nested.push(node)
  return
}

export const toNested = (titles: MarkdownTitle[]) => {
  const nested: MarkdownNestedNodeChild[] = []

  for (const title of titles) {
    placeInNested(nested, title)
  }

  return nested
}

export const Markdown: FC<MarkdownProps> = ({ source, className, titlesDispatch }) => {
  const [markdown, setMarkdown] = useState(undefined as undefined | ReactNode)

  // biome-ignore lint/correctness/useExhaustiveDependencies: Once
  useEffect(() => {
    const node = (
      <ReactMarkdown
        components={titlesDispatch && createTitleProxy(titlesDispatch)}
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw, rehypeSanitize, rehypeHighlight]}>
        {source}
      </ReactMarkdown>
    )

    setMarkdown(node)
  }, [])

  return <div className={clsx('prose', className)}>{markdown}</div>
}
