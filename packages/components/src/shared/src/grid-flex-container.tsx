import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC, HTMLAttributes, ReactNode } from 'react'

const gridFlexVariants = cva('w-full', {
  variants: {
    layout: {
      grid: 'grid',
      flex: 'flex',
    },
    direction: {
      row: 'flex-row',
      'row-reverse': 'flex-row-reverse',
      column: 'flex-col',
      'column-reverse': 'flex-col-reverse',
    },
    wrap: {
      nowrap: 'flex-nowrap',
      wrap: 'flex-wrap',
      'wrap-reverse': 'flex-wrap-reverse',
    },
    gap: {
      none: 'gap-0',
      sm: 'gap-2 sm:gap-4',
      md: 'gap-4 md:gap-6',
      lg: 'gap-6 lg:gap-8',
      xl: 'gap-8 xl:gap-12',
    },
    columns: {
      1: 'grid-cols-1',
      2: 'grid-cols-1 sm:grid-cols-2',
      3: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
      6: 'grid-cols-1 md:grid-cols-3 lg:grid-cols-6',
      12: 'grid-cols-12',
    },
    align: {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
      stretch: 'items-stretch',
    },
    justify: {
      start: 'justify-start',
      center: 'justify-center',
      end: 'justify-end',
      between: 'justify-between',
      around: 'justify-around',
      evenly: 'justify-evenly',
    },
  },
  defaultVariants: {
    layout: 'grid',
    gap: 'md',
    columns: 3,
    align: 'stretch',
    justify: 'start',
    direction: 'row',
    wrap: 'nowrap',
  },
})

interface GridFlexContainerProps extends HTMLAttributes<HTMLElement>, VariantProps<typeof gridFlexVariants> {
  children: ReactNode
  as?: 'div' | 'section'
  className?: string
}

const GridFlexContainer: FC<GridFlexContainerProps> = ({
  children,
  className,
  layout,
  direction,
  wrap,
  gap,
  columns,
  align,
  justify,
  as: Component = 'div',
  ...props
}) => {
  return (
    <Component
      className={clsx(gridFlexVariants({ layout, direction, wrap, gap, columns, align, justify }), className)}
      {...props}>
      {children}
    </Component>
  )
}

export { GridFlexContainer, gridFlexVariants, type GridFlexContainerProps }
