'use client'
import {
  Content as Accordion<PERSON>ontent,
  <PERSON>er as Accordion<PERSON>eader,
  Item as Accordion<PERSON><PERSON>,
  Root as AccordionRoot,
  Trigger as AccordionTrigger,
} from '@radix-ui/react-accordion'
import { CollapseIcon, ExpandIcon } from '@venture-vibe/icons'
import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC } from 'react'
import { useMemo } from 'react'

const faqVariants = cva(['w-full', 'rounded-[16px]'], {
  variants: {
    color: {
      light: ['bg-white', 'text-dark-gray'],
      dark: ['bg-dark-gray', 'text-white'],
    },
  },
  defaultVariants: {
    color: 'light',
  },
})

const faqItemVariants = cva(['border p-6', 'text-[16px]', 'overflow-hidden', 'transition-colors'], {
  variants: {
    position: {
      first: ['rounded-t-[16px]'],
      middle: ['rounded-none'],
      last: ['rounded-b-[16px]'],
      single: ['rounded-[16px]'],
    },
    color: {
      light: ['border-light-gray', 'text-dark-gray'],
      dark: ['border-spanish-gray', 'text-white'],
    },
  },
  defaultVariants: {
    position: 'middle',
    color: 'light',
  },
})
const faqContentVariants = cva(
  ['text-[15px]', 'leading-relaxed', 'data-[state=open]:animate-slideDown', 'data-[state=closed]:animate-slideUp'],
  {
    variants: {
      color: {
        light: ['text-dark-gray'],
        dark: ['text-light-gray'],
      },
    },
    defaultVariants: {
      color: 'light',
    },
  },
)

interface FaqItem {
  id: string
  question: string
  answer: string
}

interface FaqProps extends VariantProps<typeof faqVariants> {
  items: FaqItem[]
  type?: 'single' | 'multiple'
  collapsible?: boolean
  className?: string
}

const Faq: FC<FaqProps> = ({ items, type = 'single', collapsible = true, color, className, ...props }) => {
  const faqItems = useMemo(() => {
    const getItemPosition = (index: number, total: number) => {
      if (total === 1) {
        return 'single'
      }
      if (index === 0) {
        return 'first'
      }
      if (index === total - 1) {
        return 'last'
      }
      return 'middle'
    }

    return items.map((item, index) => {
      const position = getItemPosition(index, items.length)
      return (
        <AccordionItem key={item.id} value={item.id} className={faqItemVariants({ position, color })}>
          <AccordionHeader>
            <AccordionTrigger
              className={clsx(
                'group flex items-center justify-between',
                'w-full',
                'gap-3',
                'px-0 py-4',
                'text-left font-medium',
                'outline-none transition-colors',
                'focus-visible:ring-2 focus-visible:ring-offset-0',
              )}>
              <span>{item.question}</span>
              <span className='relative'>
                <ExpandIcon className='group-data-[state=open]:hidden' />
                <CollapseIcon className='hidden group-data-[state=open]:block' />
              </span>
            </AccordionTrigger>
          </AccordionHeader>
          <AccordionContent className={faqContentVariants({ color })}>{item.answer}</AccordionContent>
        </AccordionItem>
      )
    })
  }, [items, color])

  return (
    <AccordionRoot type={type} collapsible={collapsible} className={faqVariants({ color, className })} {...props}>
      {faqItems}
    </AccordionRoot>
  )
}

export { Faq, faqVariants, faqItemVariants, faqContentVariants, type FaqItem, type FaqProps }
