import { ChevronDownIcon } from '@venture-vibe/icons'
import type React from 'react'
import { Link } from './link'

interface BreadcrumbItem {
  label: string
  url: string
}

interface BreadcrumbProps {
  items: BreadcrumbItem[]
}

const SLICE_LIMIT = 30

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items }) => {
  return (
    <nav className='flex' aria-label='Breadcrumb'>
      <ol className='inline-flex items-center space-x-1 md:space-x-3'>
        {items.map((item, index) => (
          <li key={Number(index)} className='inline-flex items-center'>
            {index > 0 && <ChevronDownIcon className='w-7 h-7 -rotate-90 text-gray mr-2' />}
            <Link
              href={item.url}
              className='inline-flex items-center text-sm font-medium text-black hover:text-bright-blue hover:underline'>
              {item.label.length > SLICE_LIMIT ? `${item.label.slice(0, SLICE_LIMIT)}...` : item.label}
            </Link>
          </li>
        ))}
      </ol>
    </nav>
  )
}

export { Breadcrumb, type BreadcrumbItem, type BreadcrumbProps }
