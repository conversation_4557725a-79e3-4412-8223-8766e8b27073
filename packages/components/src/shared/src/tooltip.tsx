'use client'

import { Arrow, Content, Portal, Provider, Root, Trigger } from '@radix-ui/react-tooltip'
import { cva, type VariantProps } from '@venture-vibe/utils'
import type { FC, ReactNode } from 'react'

const tooltipContentVariants = cva(
  [
    'select-none rounded-max leading-none',
    'will-change-[transform,opacity]',
    'data-[state=delayed-open]:data-[side=bottom]:animate-slideUpAndFade',
    'data-[state=delayed-open]:data-[side=left]:animate-slideRightAndFade',
    'data-[state=delayed-open]:data-[side=right]:animate-slideLeftAndFade',
    'data-[state=delayed-open]:data-[side=top]:animate-slideDownAndFade',
  ],
  {
    variants: {
      color: {
        primary: ['bg-spring-bud text-black'],
        secondary: ['bg-orange text-white'],
        tertiary: ['bg-spring-stroke text-black'],
        quaternary: ['bg-light-orange text-black'],
      },
      size: {
        sm: 'px-2 py-1 text-xs',
        default: 'px-7.5 py-2.5 text-7.5',
        lg: 'px-4 py-3 text-base',
      },
    },
    defaultVariants: {
      color: 'primary',
      size: 'default',
    },
  },
)

const tooltipArrowVariants = cva([], {
  variants: {
    color: {
      primary: ['fill-spring-bud'],
      secondary: ['fill-orange'],
      tertiary: ['fill-spring-stroke'],
      quaternary: ['fill-light-orange'],
    },
  },
  defaultVariants: {
    color: 'primary',
  },
})

interface TooltipProps extends VariantProps<typeof tooltipContentVariants> {
  children: ReactNode
  content: ReactNode
  sideOffset?: number
  delayDuration?: number
  skipDelayDuration?: number
  showArrow?: boolean
  className?: string
}

const Tooltip: FC<TooltipProps> = ({
  children,
  content,
  color = 'primary',
  size = 'default',
  sideOffset = 5,
  delayDuration = 700,
  skipDelayDuration = 300,
  showArrow,
  className,
  ...props
}) => {
  return (
    <Provider delayDuration={delayDuration} skipDelayDuration={skipDelayDuration}>
      <Root>
        <Trigger asChild>{children}</Trigger>
        <Portal>
          <Content className={tooltipContentVariants({ color, size, className })} sideOffset={sideOffset} {...props}>
            {content}
            {showArrow && <Arrow className={tooltipArrowVariants({ color })} />}
          </Content>
        </Portal>
      </Root>
    </Provider>
  )
}

export { Tooltip, type TooltipProps, tooltipContentVariants, tooltipArrowVariants }
