import { type FC, type HTMLAttributes, useId } from 'react'

export interface NoiseProps extends HTMLAttributes<HTMLDivElement> {}

export const Noise: FC<NoiseProps> = props => {
  const filterId = useId()
  const filterUrl = `url(#${filterId})`

  return (
    <div {...props}>
      <svg width='100%' height='100%' xmlns='http://www.w3.org/2000/svg' style={{ pointerEvents: 'none' }}>
        <title>Noise</title>
        <filter
          id={filterId}
          x='0'
          y='0'
          width='100%'
          height='100%'
          filterUnits='objectBoundingBox'
          colorInterpolationFilters='sRGB'>
          <feTurbulence
            type='fractalNoise'
            baseFrequency='0.167'
            stitchTiles='stitch'
            numOctaves='3'
            seed='968'
            result='noise'
          />
          <feColorMatrix in='noise' type='luminanceToAlpha' result='alphaNoise' />
          <feComponentTransfer in='alphaNoise' result='thresholdNoise'>
            <feFuncA
              type='discrete'
              tableValues='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 '
            />
          </feComponentTransfer>
          <feFlood floodColor='rgba(150, 150, 150, 0.25)' result='noiseColor' />
          <feComposite operator='in' in='noiseColor' in2='thresholdNoise' />
        </filter>
        <rect width='100%' height='100%' fill='transparent' filter={filterUrl} />
      </svg>
    </div>
  )
}
