// import Image from 'next/image'
/** biome-ignore-all lint/performance/noImgElement: Reduntant */
/** biome-ignore-all lint/a11y/useAltText: <immediate> */
/** biome-ignore-all lint/nursery/useImageSize: <immediate> */

// biome-ignore lint/nursery/noUnresolvedImports: Redundant
import { Fragment, type ReactNode } from 'react'
import { Title } from './title'

interface HeaderProps {
  level: 1 | 2 | 3 | 4 | 5 | 6
  children: ReactNode
  align?: 'left' | 'center' | 'right' | 'justify' | undefined
}

interface ListItemProps {
  children: ReactNode
  isOrdered: boolean
}

import type { BlobType } from '@venture-vibe/utils'
import { Text } from './text'

interface ParagraphProps {
  children: ReactNode
  align?: 'left' | 'center' | 'right' | 'justify' | undefined
}

function Paragraph({ children, align }: ParagraphProps) {
  return (
    <Text as='p' {...(align ? { align } : {})}>
      {children}
    </Text>
  )
}

function ListItem({ children }: ListItemProps) {
  return <li>{children}</li>
}

function Header({ level, children, align }: HeaderProps) {
  const Tag = `h${level}` as `h${1 | 2 | 3 | 4 | 5 | 6}`
  const titleProps: BlobType = { as: Tag, preset: Tag }
  if (align !== undefined) {
    titleProps.align = align
  }
  return <Title {...titleProps}>{children}</Title>
}

// Define types for Quill Delta structure
interface OpAttributes {
  bold?: boolean
  italic?: boolean
  header?: number
  list?: 'ordered' | 'bullet' | undefined
  underline?: boolean
  color?: string
  background?: string
  script?: 'super' | 'sub'
  link?: string
  align?: 'left' | 'center' | 'right' | 'justify'
  blockquote?: boolean
  'code-block'?: boolean
  formula?: string
  image?: string
  video?: string
  indent?: number
}

interface DeltaOp {
  insert: string | { image: string } | { video: string } | { formula: string }
  attributes?: OpAttributes | undefined
}

export interface Delta {
  ops: DeltaOp[]
}

export interface QuillDeltaRendererProps {
  delta: Delta
}

export function QuillDeltaRenderer({ delta }: QuillDeltaRendererProps) {
  if (!(delta && Array.isArray(delta.ops))) {
    return <Paragraph>Invalid Delta content.</Paragraph>
  }

  const renderedBlocks: ReactNode[] = []
  let currentBlockContent: ReactNode[] = []
  let currentBlockType: 'paragraph' | 'header' | 'list' | 'blockquote' | 'code-block' = 'paragraph'
  let currentHeaderLevel: number | undefined
  let currentListType: 'ordered' | 'bullet' | undefined
  let currentBlockAlign: 'left' | 'center' | 'right' | 'justify' | undefined
  let listItems: ReactNode[] = []

  const flushBlock = () => {
    if (currentBlockContent.length === 0 && listItems.length === 0) {
      return // Nothing to flush
    }

    if (currentBlockType === 'header' && currentHeaderLevel) {
      renderedBlocks.push(
        <Fragment key={renderedBlocks.length}>
          <Header level={currentHeaderLevel as 1 | 2 | 3 | 4 | 5 | 6} align={currentBlockAlign}>
            {currentBlockContent}
          </Header>
        </Fragment>,
      )
    } else if (currentBlockType === 'list') {
      if (currentListType === 'ordered') {
        renderedBlocks.push(<ol key={renderedBlocks.length}>{listItems}</ol>)
      } else if (currentListType === 'bullet') {
        renderedBlocks.push(<ul key={renderedBlocks.length}>{listItems}</ul>)
      }
    } else if (currentBlockType === 'blockquote') {
      renderedBlocks.push(
        <blockquote key={renderedBlocks.length} className='border-l-4 border-gray-300 pl-4 italic my-4'>
          {currentBlockContent}
        </blockquote>,
      )
    } else if (currentBlockType === 'code-block') {
      renderedBlocks.push(
        <pre key={renderedBlocks.length} className='bg-gray-100 p-4 rounded-md my-4 overflow-x-auto'>
          <code className='text-sm'>{currentBlockContent}</code>
        </pre>,
      )
    } else {
      renderedBlocks.push(
        <Fragment key={renderedBlocks.length}>
          <Paragraph align={currentBlockAlign}>
            {currentBlockContent.length > 0 ? currentBlockContent : <br />}
          </Paragraph>
        </Fragment>,
      )
    }

    currentBlockContent = []
    currentBlockType = 'paragraph'
    currentHeaderLevel = undefined
    currentListType = undefined
    currentBlockAlign = undefined
    listItems = []
  }

  // Helper to handle inline formatting
  function renderInlineContent(text: string, attributes: OpAttributes, index: number): ReactNode {
    let content: ReactNode = text
    if (attributes.bold) {
      content = <strong key={`bold-${index}`}>{content}</strong>
    }
    if (attributes.italic) {
      content = <em key={`italic-${index}`}>{content}</em>
    }
    if (attributes.underline) {
      content = <u key={`underline-${index}`}>{content}</u>
    }
    if (attributes.color) {
      content = (
        <span key={`color-${index}`} style={{ color: attributes.color }}>
          {content}
        </span>
      )
    }
    if (attributes.background) {
      content = (
        <span key={`background-${index}`} style={{ backgroundColor: attributes.background }}>
          {content}
        </span>
      )
    }
    if (attributes.script === 'super') {
      content = <sup key={`super-${index}`}>{content}</sup>
    }
    if (attributes.script === 'sub') {
      content = <sub key={`sub-${index}`}>{content}</sub>
    }
    if (attributes.link) {
      content = (
        <a href={attributes.link} key={`link-${index}`} className='text-blue-500 underline'>
          {content}
        </a>
      )
    }
    return content
  }

  // Helper to handle embeds
  function handleEmbed(op: DeltaOp, index: number) {
    flushBlock()
    if (typeof op.insert === 'object' && op.insert !== null && 'image' in op.insert) {
      renderedBlocks.push(
        <img
          key={`image-${index}`}
          src='https://framerusercontent.com/images/FiOGy1MAeTdhCoSGTz3kmYUkrU.png?scale-down-to=1024'
          alt='alt'
          width={200}
          height={200}
          className='w-full h-auto object-contain'
        />,

        // <Image
        //   key={`image-${index}`}
        //   src={(op.insert as { image: string }).image}
        //   alt={`image-${index}`}
        //   width={800}
        //   height={600}
        //   className='max-w-full h-auto my-4'
        // />,
      )
    } else if (typeof op.insert === 'object' && op.insert !== null && 'video' in op.insert) {
      const videoUrl = (op.insert as { video: string }).video.includes('youtube.com/embed')
        ? (op.insert as { video: string }).video
        : `https://www.youtube.com/embed/${(op.insert as { video: string }).video.split('v=')[1]}`
      renderedBlocks.push(
        <div key={`video-${index}`} className='my-4 aspect-video'>
          <iframe
            src={videoUrl}
            frameBorder='0'
            allow='accelerometer; autoplay; clipboard-write; encrypted-m`edia; gyroscope; picture-in-picture'
            allowFullScreen
            className='w-full h-full'
            title='Embedded video'
          />
        </div>,
      )
    } else if (typeof op.insert === 'object' && op.insert !== null && 'formula' in op.insert) {
      renderedBlocks.push(
        <p key={`formula-${index}`} className='my-4 text-red-500'>
          Formula (rendering not fully supported): {(op.insert as { formula: string }).formula}
        </p>,
      )
    }
  }

  // Helper to handle block type changes
  function setBlockType(attributes: OpAttributes) {
    if (attributes.header) {
      currentBlockType = 'header'
      currentHeaderLevel = attributes.header as 1 | 2 | 3 | 4 | 5 | 6
    } else if (attributes.list) {
      currentBlockType = 'list'
      currentListType = attributes.list
    } else if (attributes.blockquote) {
      currentBlockType = 'blockquote'
    } else if (attributes['code-block']) {
      currentBlockType = 'code-block'
    } else {
      currentBlockType = 'paragraph'
    }
    currentBlockAlign = attributes.align
  }

  delta.ops.forEach((op, index) => {
    if (typeof op.insert === 'object' && op.insert !== null) {
      handleEmbed(op, index)
      return
    }

    const text = op.insert as string
    const attributes = op.attributes || {}
    const lines = String(text).split('\n')

    lines.forEach((line, lineIndex) => {
      if (lineIndex > 0) {
        flushBlock()
        setBlockType(attributes)
      }

      if (line.length > 0) {
        const inlineContent = renderInlineContent(line, attributes, index)
        if (currentBlockType === 'list') {
          listItems.push(
            <ListItem
              key={`list-item-${renderedBlocks.length}-${listItems.length}`}
              isOrdered={currentListType === 'ordered'}>
              {inlineContent}
            </ListItem>,
          )
        } else {
          currentBlockContent.push(inlineContent)
        }
      } else if (lineIndex > 0 && lines.length > 1) {
        flushBlock()
      }
    })
  })

  // Flush any remaining content after the loop
  flushBlock()

  return <>{renderedBlocks}</>
}
