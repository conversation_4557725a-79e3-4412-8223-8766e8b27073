import { clsx, cva, type VariantProps } from '@venture-vibe/utils'
import type { FC, HTMLAttributes, ReactNode } from 'react'

const backgroundSectionVariants = cva(['relative', 'w-full', 'overflow-hidden'], {
  variants: {
    padding: {
      none: 'p-0',
      sm: 'py-8 sm:py-12 md:py-16',
      md: 'py-16 sm:py-20 md:py-24',
      lg: 'py-24 sm:py-28 md:py-32',
    },
  },
  defaultVariants: {
    padding: 'md',
  },
})

const processLayer = (layer: BackgroundSectionLayer, z: number) => {
  switch (layer.type) {
    case 'color':
      return (
        <div
          key={z}
          className={clsx('absolute inset-0', layer.className)}
          style={{
            backgroundColor: layer.color,
            opacity: layer.opacity,
            zIndex: z,
          }}
        />
      )
    case 'image':
      return (
        <div
          key={z}
          className={clsx('absolute inset-0 bg-cover bg-center', layer.className)}
          style={{
            backgroundImage: `url(${layer.image})`,
            opacity: layer.opacity,
            zIndex: z,
          }}
        />
      )
    case 'video':
      return (
        <video
          key={z}
          className={clsx('absolute inset-0 w-full h-full object-cover', layer.className)}
          style={{
            opacity: layer.opacity,
            zIndex: z,
          }}
          autoPlay
          loop
          muted
          playsInline>
          <source src={layer.video} type='video/mp4' />
        </video>
      )
    case 'node':
      return (
        <div
          key={z}
          className={clsx('absolute inset-0', layer.className)}
          style={{
            zIndex: z,
          }}>
          {layer.node}
        </div>
      )
    default:
      return 'Unknown layer type!'
  }
}

export type BackgroundSectionLayer =
  | { type: 'image'; image: string; opacity?: number; className?: string }
  | { type: 'video'; video: string; opacity?: number; className?: string }
  | { type: 'color'; color: string; opacity?: number; className?: string }
  | { type: 'node'; node: ReactNode; className?: string }

export interface BackgroundSectionCustomProps {
  as?: 'section' | 'div'
  zStart?: number
  layers: BackgroundSectionLayer[]
}

export type BackgroundSectionProps = HTMLAttributes<HTMLElement> &
  VariantProps<typeof backgroundSectionVariants> &
  BackgroundSectionCustomProps & {
    children?: ReactNode
    className?: string
  }

export const BackgroundSection: FC<BackgroundSectionProps> = ({
  as: Component = 'section',
  children,
  className,
  layers,
  zStart = 0,
  padding,
  ...props
}) => {
  const processedLayers = layers.map((v, i) => processLayer(v, zStart + i))

  return (
    <Component className={clsx(backgroundSectionVariants({ padding }), className)} {...props}>
      {processedLayers}
      <div style={{ zIndex: zStart + layers.length }} className='relative'>
        {children}
      </div>
    </Component>
  )
}
