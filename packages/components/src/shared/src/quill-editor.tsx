'use client'

import Quill, { type Delta, type EmitterSource } from 'quill/core'
import {
  createContext,
  type Dispatch,
  type FC,
  type ReactNode,
  type RefObject,
  type SetStateAction,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react'
import 'quill/dist/quill.snow.css'

const TOOLBAR_OPTIONS = [
  [{ size: ['small', false, 'large', 'huge'] }],
  // biome-ignore lint/nursery/noMagicNumbers: Reduntant
  [{ header: [1, 2, 3, 4, 5, 6, false] }],
  [{ font: [] }],
  ['bold', 'italic', 'underline', 'strike'],
  [{ color: [] }, { background: [] }],
  ['blockquote', 'code-block'],
  [{ list: 'ordered' }, { list: 'bullet' }],
  [{ indent: '-1' }, { indent: '+1' }],
  [{ script: 'sub' }, { script: 'super' }],
  [{ align: [] }],
  ['link', 'image', 'video'],
  ['clean'],
]

const FORMATS = [
  'header',
  'font',
  'size',
  'bold',
  'italic',
  'underline',
  'strike',
  'color',
  'background',
  'script',
  'blockquote',
  'code-block',
  'list',
  'indent',
  'align',
  'link',
  'image',
  'video',
]

export interface QuillEditorProps {
  className?: string
}

export interface QuiellEditorContext {
  quill: Quill | null
  setQuill: Dispatch<SetStateAction<Quill | null>>
  editorRef: RefObject<null>
}

export const QuiellEditorContext = createContext<QuiellEditorContext | undefined>(undefined)

export function QuiellEditorContextProvider({ children }: { children: ReactNode }) {
  const [quill, setQuill] = useState(null as Quill | null)
  const editorRef = useRef(null)

  const value = { quill, setQuill, editorRef }

  return <QuiellEditorContext.Provider value={value}>{children}</QuiellEditorContext.Provider>
}

export function useQuiellEditor() {
  const context = useContext(QuiellEditorContext)

  if (context === undefined) {
    throw new Error('QuiellEditorContext')
  }

  return context
}

export const QuillEditor: FC<QuillEditorProps> = props => {
  const { editorRef, quill, setQuill } = useQuiellEditor()

  // biome-ignore lint/correctness/useExhaustiveDependencies: Once
  useEffect(() => {
    if (editorRef.current === null) {
      return
    }
    const editor = new Quill(editorRef.current, {
      theme: 'snow',
      modules: {
        toolbar: TOOLBAR_OPTIONS,
      },
      formats: FORMATS,
      placeholder: 'İçeriğinizi buraya yazın...',
    })

    setQuill(editor)

    return () => {
      if (editor) {
        editor.container.innerHTML = ''
      }
    }
  }, [])

  useEffect(() => {
    if (quill === null) {
      return
    }

    const handleChange = (_delta: Delta, _oldDelta: Delta, source: EmitterSource) => {
      if (source === 'user') {
        // biome-ignore lint/suspicious/noConsole: Reduntant
        console.log(quill.getContents())
      }
    }

    quill.on('text-change', handleChange)

    return () => {
      quill.off('text-change', handleChange)
    }
  }, [quill])

  return (
    <div className='editor-container'>
      <div ref={editorRef} className={props.className} />
    </div>
  )
}
