import type { FC } from 'react'
import { Link } from './link'

const MAX_DESCRIPTION_LENGTH = 150

interface PersonCardProps {
  name: string
  title: string
  description: string
  image: string
  link?: string
}

const PersonCard: FC<PersonCardProps> = ({ name, title, description, image, link = '' }) => {
  return (
    <Link href={link} className='flex group'>
      <div className='relative max-w-200 sm:max-w-150 w-full h-225 bg-cover bg-center rounded-2xl overflow-hidden'>
        <div
          className='absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110'
          style={{ backgroundImage: `url(${image})` }}
        />
        <div className='absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent' />
        <div className='relative z-10 p-6 flex flex-col justify-end h-full text-white'>
          <div>
            <h3 className='text-lg font-semibold'>{name}</h3>
            <p className='text-sm text-gray-300'>{title}</p>
            <p className='mt-2 text-sm'>
              {description.length > MAX_DESCRIPTION_LENGTH
                ? `${description.slice(0, MAX_DESCRIPTION_LENGTH)}...`
                : description}
            </p>
          </div>
        </div>
      </div>
    </Link>
  )
}

export { PersonCard, type PersonCardProps }
