'use client'

import { type FC, useId } from 'react'

export interface ImageMaskCustomProps {
  imageHref: string
  maskColor: string
  blur?: boolean
}

export type ImageMaskProps = ImageMaskCustomProps

export const ImageMask: FC<ImageMaskProps> = props => {
  const { imageHref, maskColor, blur = false } = props

  const maskFilterId = useId()

  return (
    <svg role='img' aria-label='mask' className='w-full h-full'>
      <filter id={maskFilterId} x='-20%' y='-20%' width='150%' height='150%'>
        <feFlood floodColor={maskColor} result='flood' />
        <feComposite in='flood' in2='SourceAlpha' operator='in' />
        {blur && <feGaussianBlur stdDeviation='3.5' edgeMode='duplicate' />}
      </filter>
      <image
        x='50%'
        y='50%'
        className='w-[96%] h-[96%] transform -translate-y-[48%] -translate-x-[48%]'
        href={imageHref}
        xlinkHref={imageHref}
        filter={`url('#${maskFilterId}')`}
      />
    </svg>
  )
}
