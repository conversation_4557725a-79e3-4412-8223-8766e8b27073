import type { Get, Paths } from 'type-fest'
import type { BlobType } from '../types/common'

const ANY_NUMBER = /[0-9]+/

export const getProp = <
  T extends object, // object type
  K extends Exclude<Paths<NoInfer<T>>, number>,
>(
  obj: T,
  key: K,
) => {
  return key.split('.').reduce((t, p) => t?.[p as keyof typeof t] as BlobType, obj) as Get<T, K>
}

export const setProp = <
  T extends object, // object type
  K extends Exclude<Paths<NoInfer<T>>, number>,
>(
  obj: T,
  key: Exclude<Paths<NoInfer<T>>, number>,
  value: BlobType,
) => {
  const keyPaths = key.split('.')
  const lastKey = keyPaths.pop() as string

  let target = obj

  for (const rawKeyPath of keyPaths) {
    const keyPath = rawKeyPath as keyof typeof target

    if (!target[keyPath]) {
      if ((keyPath as string).match(ANY_NUMBER)) {
        target[keyPath] = [] as BlobType
      } else {
        target[keyPath] = {} as BlobType
      }
    }

    target = target[keyPath] as BlobType
  }

  target[lastKey as keyof typeof target] = value
}
