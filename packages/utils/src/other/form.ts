/** biome-ignore-all lint/nursery/noExcessiveLinesPerFunction: Redundant */
import { isFunction, type Observable, ObservableHint, observable, type PlainObject } from '@legendapp/state'
import { type Type, type } from 'arktype'
import type { Get, Merge, Paths } from 'type-fest'
import { getProp } from '../other/get-set'
import type { BlobType, EmptyType } from '../types/common'

export type CreateFormStoreOptions<T extends object, R extends BlobType = T, E = EmptyType> = {
  validator: Type<T>
  initialValue?: Partial<NoInfer<T>>
  keys: Paths<NoInfer<T>>[]

  onChange?: <K extends Exclude<Paths<T>, number>>(key: K, value: Get<T, K> | undefined) => Get<T, K> | void | undefined
  onError?: (errorType: Exclude<Paths<T>, number> | 'all', errors: type.errors) => void | Promise<void>
  onSuccess?: (values: T, successType: 'clear' | 'check') => void | Promise<void>
  populate?: (values: T) => R
  onClear?: () => void | Promise<void>
} & E

export interface FormStore<T extends object, R extends BlobType = T> {
  init: boolean
  loading: boolean

  isEmpty: boolean
  values: Partial<T>
  errors: Record<Paths<T>, false | PlainObject<type.errors>>
  dirty: Record<Paths<T>, boolean>

  populated: () => R

  use: PlainObject<
    <K extends Exclude<Paths<T>, number>>(
      key: K,
      options?: {
        checker?: Type<Get<T, K>>
        defaultValue?: Get<T, K>
        removeIfDefault?: boolean
      },
    ) => {
      value: Get<T, K>
      error: boolean
      set: (value?: ((prev: Get<T, K>) => Get<T, K> | undefined | void) | Get<T, K> | undefined) => void
      setDirty: () => void
      reset: () => void
    }
  >
  clear: PlainObject<(disableSubmit?: boolean) => Promise<boolean | null>>
  check: PlainObject<(clear?: boolean) => Promise<boolean | null | string>>
}

export const createFormStore = <T extends object, R extends BlobType = T, E = EmptyType>({
  validator,
  initialValue,
  keys,
  onChange,
  onError,
  onSuccess,
  onClear,
  populate,
  ...others
}: CreateFormStoreOptions<T, R, E>) => {
  const valuesBase = initialValue ?? ({} as T)
  const errorsBase = keys.reduce(
    (acc, key) => {
      acc[key] = false
      return acc
    },
    {} as Record<Paths<T>, boolean>,
  )
  const dirtyBase = keys.reduce(
    (acc, key) => {
      acc[key] = false
      return acc
    },
    {} as Record<Paths<T>, boolean>,
  )

  const store = observable<FormStore<BlobType>>({
    init: false,
    loading: false,
    values: structuredClone(valuesBase),
    errors: structuredClone(errorsBase),
    dirty: structuredClone(dirtyBase),

    isEmpty: (): boolean => {
      return JSON.stringify(store.values.get()) === JSON.stringify(valuesBase)
    },

    populated: () => {
      const values = store.values.get()

      return populate?.(values) ?? values
    },

    use: ObservableHint.function(
      (key, { checker: customChecker, defaultValue, removeIfDefault = false } = {}): BlobType => {
        const targetValue = getProp(store.values, key) as Observable<BlobType>
        const targetError = getProp(store.errors, key) as Observable<boolean | PlainObject<type.errors>>
        const targetDirty = getProp(store.dirty, key) as Observable<boolean>

        const checker: Type<BlobType> = customChecker ?? (validator as BlobType).get(key)

        // biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Redundant
        const set = (rawValue: BlobType) => {
          targetDirty.set(true)

          const baseDefaultValue = defaultValue ?? getProp(valuesBase, key)
          const value = (isFunction(rawValue) ? rawValue(targetValue.peek()) : rawValue) ?? baseDefaultValue

          if (checker) {
            const out = checker(value)

            if (out instanceof type.errors) {
              const prevError = targetError.peek() as unknown as type.errors | false

              const prevErrorJson = (prevError === false ? [] : prevError?.toJSON?.()) ?? []
              const newErrorJson = out?.toJSON?.() ?? []

              const isSame =
                prevErrorJson.length === newErrorJson.length &&
                prevErrorJson.every((item: BlobType, index: number) => {
                  return item.description === (newErrorJson[index] as BlobType)?.description
                })

              if (!isSame) {
                targetError.set(ObservableHint.opaque(out as BlobType))
              }

              onError?.(key, out)
            } else {
              targetError.set(false)
            }
          }

          if (removeIfDefault && value === baseDefaultValue) {
            onChange?.(key, undefined)

            targetValue.delete()
          } else {
            targetValue.set(onChange?.(key, value) ?? value)
          }
        }

        return {
          value: targetValue.get(),
          error: targetDirty.get() && (targetError.get() instanceof type.errors || targetError.get() === true),
          set,
          setDirty: () => {
            set(getProp(store.values, key).get())
          },
          reset: () => {
            targetValue.set(getProp(valuesBase, key))
            targetError.set(false)
            targetDirty.set(false)
          },
        }
      },
    ),

    clear: ObservableHint.function(async (disableSubmit = false) => {
      store.loading.set(true)

      store.values.set(structuredClone(valuesBase))
      store.errors.set(structuredClone(errorsBase))
      store.dirty.set(structuredClone(dirtyBase))

      try {
        await onClear?.()

        if (!disableSubmit) {
          store.init.set(true)
          await onSuccess?.(structuredClone(valuesBase) as T, 'clear')
        }
      } finally {
        store.loading.set(false)
      }

      return true
    }),

    check: ObservableHint.function(async (clear = false) => {
      if (store.loading.peek()) {
        return null
      }

      store.loading.set(true)

      try {
        const out = validator(store.values.peek())

        if (out instanceof type.errors) {
          for (const key of keys) {
            const field = store.peek().use(key as never, {
              checker: (validator as BlobType).get(key),
            })

            field.setDirty()
          }

          await onError?.('all', out)

          return false
        }

        let hasError = false

        for (const [errorKey, errorValue] of Object.entries(store.errors.peek())) {
          if (errorValue) {
            hasError = true
            await onError?.(errorKey as never, errorValue as never)
          }
        }

        if (hasError) {
          return false
        }

        store.init.set(true)
        await onSuccess?.(store.values.peek() as T, 'check')

        if (clear) {
          await store.peek().clear(clear)
        }
      } catch (err) {
        return err as string
      } finally {
        store.loading.set(false)
      }

      return true
    }),

    ...others,
  })

  return store as unknown as Observable<Merge<FormStore<T, R>, E>>
}

export const inferFormStore = <T>(store: T): T extends Observable<infer F> ? F : never => store as BlobType
