{"[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "biome.enabled": true, "editor.codeActionsOnSave": {"source.action.useSortedKeys.biome": "explicit", "source.fixAll.biome": "explicit"}, "editor.defaultFormatter": "biomejs.biome", "eslint.enable": false, "files.associations": {"*.css": "css"}, "css.validate": false, "javascript.preferences.importModuleSpecifier": "shortest", "prettier.enable": false, "typescript.disableAutomaticTypeAcquisition": false, "typescript.preferences.importModuleSpecifier": "project-relative", "typescript.tsdk": "node_modules/typescript/lib", "tailwindCSS.classFunctions": ["cva", "cx"], "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}}