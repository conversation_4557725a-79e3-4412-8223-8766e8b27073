import { Button, Tooltip } from '@venture-vibe/components/shared'

export default function TooltipPage() {
  return (
    <div className='space-y-8 p-8'>
      <section className='space-y-4'>
        <Tooltip content='This is the default tooltip'>
          <Button>Hover me (Default)</Button>
        </Tooltip>
      </section>
      <section className='space-y-4'>
        <div className='flex flex-wrap gap-4'>
          <Tooltip content='Primary color tooltip' color='primary'>
            <Button>Primary</Button>
          </Tooltip>
          <Tooltip content='Secondary color tooltip' color='secondary'>
            <Button>Secondary</Button>
          </Tooltip>
          <Tooltip content='Tertiary color tooltip' color='tertiary'>
            <Button>Tertiary</Button>
          </Tooltip>
          <Tooltip content='Quaternary color tooltip' color='quaternary'>
            <Button>Quaternary</Button>
          </Tooltip>
        </div>
      </section>
      <section className='space-y-4'>
        <div className='flex flex-wrap gap-4'>
          <Tooltip content='Small tooltip' size='sm'>
            <Button>Small</Button>
          </Tooltip>
          <Tooltip content='Default size tooltip' size='default'>
            <Button>Default</Button>
          </Tooltip>
          <Tooltip content='Large tooltip' size='lg'>
            <Button>Large</Button>
          </Tooltip>
        </div>
      </section>
      <section className='space-y-4'>
        <div className='flex flex-wrap gap-4'>
          <Tooltip content='Primary with arrow' color='primary' showArrow>
            <Button>Primary + Arrow</Button>
          </Tooltip>
          <Tooltip content='Secondary with arrow' color='secondary' showArrow>
            <Button>Secondary + Arrow</Button>
          </Tooltip>
          <Tooltip content='Tertiary with arrow' color='tertiary' showArrow>
            <Button>Tertiary + Arrow</Button>
          </Tooltip>
          <Tooltip content='Quaternary with arrow' color='quaternary' showArrow>
            <Button>Quaternary + Arrow</Button>
          </Tooltip>
        </div>
      </section>
      <section className='space-y-4'>
        <div className='space-y-4'>
          <div className='flex flex-wrap gap-4'>
            <Tooltip content='Small Primary' color='primary' size='sm'>
              <Button size='sm'>Small Primary</Button>
            </Tooltip>
            <Tooltip content='Small Secondary' color='secondary' size='sm'>
              <Button size='sm'>Small Secondary</Button>
            </Tooltip>
            <Tooltip content='Small Tertiary' color='tertiary' size='sm'>
              <Button size='sm'>Small Tertiary</Button>
            </Tooltip>
            <Tooltip content='Small Quaternary' color='quaternary' size='sm'>
              <Button size='sm'>Small Quaternary</Button>
            </Tooltip>
          </div>
          <div className='flex flex-wrap gap-4'>
            <Tooltip content='Large Primary tooltip with more content' color='primary' size='lg'>
              <Button size='lg'>Large Primary</Button>
            </Tooltip>
            <Tooltip content='Large Secondary tooltip with more content' color='secondary' size='lg'>
              <Button size='lg'>Large Secondary</Button>
            </Tooltip>
            <Tooltip content='Large Tertiary tooltip with more content' color='tertiary' size='lg'>
              <Button size='lg'>Large Tertiary</Button>
            </Tooltip>
            <Tooltip content='Large Quaternary tooltip with more content' color='quaternary' size='lg'>
              <Button size='lg'>Large Quaternary</Button>
            </Tooltip>
          </div>
        </div>
      </section>
      <section className='space-y-4'>
        <div className='flex flex-wrap gap-4'>
          <Tooltip content='Fast tooltip (100ms delay)' delayDuration={100}>
            <Button>Fast Delay</Button>
          </Tooltip>
          <Tooltip content='Slow tooltip (1500ms delay)' delayDuration={1500}>
            <Button>Slow Delay</Button>
          </Tooltip>
          <Tooltip content='Far offset tooltip' sideOffset={20}>
            <Button>Far Offset</Button>
          </Tooltip>
          <Tooltip content='Close offset tooltip' sideOffset={2}>
            <Button>Close Offset</Button>
          </Tooltip>
        </div>
      </section>
      <section className='space-y-4'>
        <div className='flex flex-wrap gap-4'>
          <Tooltip
            content='Large primary with arrow, fast delay'
            color='primary'
            size='lg'
            showArrow
            delayDuration={200}>
            <Button>Complex 1</Button>
          </Tooltip>

          <Tooltip
            content='Small secondary with arrow, far offset'
            color='secondary'
            size='sm'
            showArrow
            sideOffset={15}>
            <Button>Complex 2</Button>
          </Tooltip>

          <Tooltip
            content='Large tertiary, slow delay, close offset'
            color='tertiary'
            size='lg'
            delayDuration={1000}
            sideOffset={3}>
            <Button>Complex 3</Button>
          </Tooltip>

          <Tooltip
            content='Quaternary with all custom props'
            color='quaternary'
            size='default'
            showArrow
            delayDuration={500}
            sideOffset={10}
            skipDelayDuration={100}>
            <Button>Complex 4</Button>
          </Tooltip>
        </div>
      </section>
      <section className='space-y-4'>
        <div className='flex flex-wrap gap-4'>
          <Tooltip
            content={
              <span>
                Simple <strong>HTML</strong> content
              </span>
            }>
            <Button>HTML Content</Button>
          </Tooltip>

          <Tooltip
            content={
              <div>
                <div className='font-bold'>Rich Content</div>
                <div className='text-xs'>With multiple lines</div>
              </div>
            }>
            <Button>Rich Content</Button>
          </Tooltip>

          <Tooltip content='🎉 Emoji content with special characters! 🚀'>
            <Button>Emoji Content</Button>
          </Tooltip>

          <Tooltip content='Very long tooltip content that might wrap to multiple lines and test how the tooltip handles longer text content gracefully'>
            <Button>Long Content</Button>
          </Tooltip>
        </div>
      </section>
    </div>
  )
}
