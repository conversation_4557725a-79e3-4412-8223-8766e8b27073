'use client'

import { <PERSON><PERSON>, <PERSON>, <PERSON> } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'
import { componentSections } from './components-router/data'

export default function Home() {
  return (
    <div className={clsx('min-h-screen w-screen', 'px-4 py-12', 'bg-bg')}>
      <div className={clsx('mx-auto max-w-7xl', 'space-y-12')}>
        <div className={clsx('text-center', 'space-y-4')}>
          <Title
            as='h1'
            className={clsx('inline-block', 'bg-transparent', 'border-1 border-black', 'px-8 py-4', 'rounded-max')}>
            Venture Vibe Playground
          </Title>
          <p className='text-gray-600 text-lg'>Explore and test all available components</p>
        </div>
        <div className={clsx('grid gap-12', 'lg:grid-cols-2 xl:grid-cols-3')}>
          {componentSections.map(section => (
            <div key={section.title} className={clsx('space-y-6')}>
              <Title as='h2' className={clsx('text-center', 'border-b', 'border-gray-200', 'pb-3')}>
                {section.title}
              </Title>
              <div className={clsx('grid gap-3', 'sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-1')}>
                {section.components.map(component => (
                  <Link key={component.name} href={component.href} className='block'>
                    <Button color='outlineBlack' className={clsx('w-full', 'justify-center')}>
                      {component.name}
                    </Button>
                  </Link>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
