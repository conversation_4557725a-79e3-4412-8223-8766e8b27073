import { Text } from '@venture-vibe/components/shared'

export default function TextPage() {
  return (
    <div className='space-y-8 p-8'>
      <section className='space-y-4'>
        <Text>Default text</Text>
      </section>
      <section className='space-y-4'>
        <Text as='p' size='xs'>
          Extra Small (xs) paragraph
        </Text>
        <Text as='p' size='sm'>
          Small (sm) paragraph
        </Text>
        <Text as='p' size='md'>
          Medium (md) paragraph
        </Text>
        <Text as='p' size='lg'>
          Large (lg) paragraph
        </Text>
        <Text as='p' size='xl'>
          Extra Large (xl) paragraph
        </Text>
        <Text as='p' size='2xl'>
          2X Large (2xl) paragraph
        </Text>
        <Text as='p' size='3xl'>
          3X Large (3xl) paragraph
        </Text>
      </section>
      <section className='space-y-4'>
        <Text as='p' size='lg' thickness='thin'>
          Thin Weight Text
        </Text>
        <Text as='p' size='lg' thickness='extralight'>
          Extra Light Weight Text
        </Text>
        <Text as='p' size='lg' thickness='light'>
          Light Weight Text
        </Text>
        <Text as='p' size='lg' thickness='normal'>
          Normal Weight Text
        </Text>
        <Text as='p' size='lg' thickness='medium'>
          Medium Weight Text
        </Text>
        <Text as='p' size='lg' thickness='semibold'>
          Semibold Weight Text
        </Text>
        <Text as='p' size='lg' thickness='bold'>
          Bold Weight Text
        </Text>
        <Text as='p' size='lg' thickness='extrabold'>
          Extra Bold Weight Text
        </Text>
        <Text as='p' size='lg' thickness='black'>
          Black Weight Text
        </Text>
      </section>
      <section className='space-y-4'>
        <Text as='p' size='lg' decoration='none'>
          No Decoration Text
        </Text>
        <Text as='p' size='lg' decoration='underline'>
          Underlined Text
        </Text>
        <Text as='p' size='lg' decoration='lineThrough'>
          Line Through Text
        </Text>
      </section>
      <section className='space-y-4'>
        <div className='rounded bg-gray-100 p-4'>
          <Text as='p' size='lg' color='black'>
            Black Text on Light Background
          </Text>
        </div>
        <div className='rounded bg-gray-800 p-4'>
          <Text as='p' size='lg' color='white'>
            White Text on Dark Background
          </Text>
        </div>
      </section>
      <section className='space-y-4'>
        <Text as='p' size='lg'>
          This is a paragraph element
        </Text>
        <div className='space-x-2'>
          <Text as='span' size='lg'>
            This is a span element
          </Text>
          <Text as='span' size='lg' thickness='bold'>
            followed by another span
          </Text>
        </div>
      </section>
      <section className='space-y-4'>
        <Text as='p' size='3xl' thickness='extrabold' decoration='underline'>
          Large Bold Underlined Paragraph
        </Text>
        <Text as='p' size='2xl' thickness='light' decoration='lineThrough'>
          Medium Light Strikethrough Paragraph
        </Text>
        <div className='rounded bg-gray-900 p-4'>
          <Text as='p' size='xl' thickness='semibold' color='white' decoration='underline'>
            White Semibold Underlined Paragraph on Dark Background
          </Text>
        </div>
        <div className='space-x-2'>
          <Text as='span' size='lg' thickness='bold' decoration='underline'>
            Bold underlined span
          </Text>
          <Text as='span' size='md' thickness='normal'>
            followed by normal span
          </Text>
          <Text as='span' size='sm' thickness='light' decoration='lineThrough'>
            and light strikethrough span
          </Text>
        </div>
      </section>
      <section className='space-y-4'>
        <div className='space-y-2'>
          <Text as='p' size='xs'>
            XS: The quick brown fox jumps over the lazy dog
          </Text>
          <Text as='p' size='sm'>
            SM: The quick brown fox jumps over the lazy dog
          </Text>
          <Text as='p' size='md'>
            MD: The quick brown fox jumps over the lazy dog
          </Text>
          <Text as='p' size='lg'>
            LG: The quick brown fox jumps over the lazy dog
          </Text>
          <Text as='p' size='xl'>
            XL: The quick brown fox jumps over the lazy dog
          </Text>
          <Text as='p' size='2xl'>
            2XL: The quick brown fox jumps over the lazy dog
          </Text>
          <Text as='p' size='3xl'>
            3XL: The quick brown fox jumps over the lazy dog
          </Text>
        </div>
      </section>
      <section className='space-y-4'>
        <div className='space-y-2'>
          <Text as='p' size='lg' thickness='thin'>
            Thin: The quick brown fox jumps over the lazy dog
          </Text>
          <Text as='p' size='lg' thickness='extralight'>
            Extra Light: The quick brown fox jumps over the lazy dog
          </Text>
          <Text as='p' size='lg' thickness='light'>
            Light: The quick brown fox jumps over the lazy dog
          </Text>
          <Text as='p' size='lg' thickness='normal'>
            Normal: The quick brown fox jumps over the lazy dog
          </Text>
          <Text as='p' size='lg' thickness='medium'>
            Medium: The quick brown fox jumps over the lazy dog
          </Text>
          <Text as='p' size='lg' thickness='semibold'>
            Semibold: The quick brown fox jumps over the lazy dog
          </Text>
          <Text as='p' size='lg' thickness='bold'>
            Bold: The quick brown fox jumps over the lazy dog
          </Text>
          <Text as='p' size='lg' thickness='extrabold'>
            Extra Bold: The quick brown fox jumps over the lazy dog
          </Text>
          <Text as='p' size='lg' thickness='black'>
            Black: The quick brown fox jumps over the lazy dog
          </Text>
        </div>
      </section>
    </div>
  )
}
