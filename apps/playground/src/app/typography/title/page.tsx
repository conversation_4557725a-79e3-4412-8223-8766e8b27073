import { Title } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'

export default function TitlePage() {
  return (
    <div className='space-y-8 p-8'>
      <section className='space-y-4'>
        <h2 className={clsx('mb-4', 'text-2xl', 'font-bold')}>Default Presets (using 'as' prop)</h2>
        <Title as='h1'>Default H1 Preset</Title>
        <Title as='h2'>Default H2 Preset</Title>
        <Title as='h3'>Default H3 Preset</Title>
        <Title as='h4'>Default H4 Preset</Title>
        <Title as='h5'>Default H5 Preset</Title>
        <Title as='h6'>Default H6 Preset</Title>
      </section>
      <section className='space-y-4'>
        <h2 className={clsx('mb-4', 'text-2xl', 'font-bold')}>All Size Variants</h2>
        <Title as='h1' size='xs'>
          Extra Small (xs)
        </Title>
        <Title as='h1' size='sm'>
          Small (sm)
        </Title>
        <Title as='h1' size='md'>
          Medium (md)
        </Title>
        <Title as='h1' size='lg'>
          Large (lg)
        </Title>
        <Title as='h1' size='xl'>
          Extra Large (xl)
        </Title>
        <Title as='h1' size='2xl'>
          2X Large (2xl)
        </Title>
        <Title as='h1' size='3xl'>
          3X Large (3xl)
        </Title>
      </section>
      <section className='space-y-4'>
        <h2 className={clsx('mb-4', 'text-2xl', 'font-bold')}>All Thickness Variants</h2>
        <Title as='h2' size='xl' thickness='thin'>
          Thin Weight
        </Title>
        <Title as='h2' size='xl' thickness='extralight'>
          Extra Light Weight
        </Title>
        <Title as='h2' size='xl' thickness='light'>
          Light Weight
        </Title>
        <Title as='h2' size='xl' thickness='normal'>
          Normal Weight
        </Title>
        <Title as='h2' size='xl' thickness='medium'>
          Medium Weight
        </Title>
        <Title as='h2' size='xl' thickness='semibold'>
          Semibold Weight
        </Title>
        <Title as='h2' size='xl' thickness='bold'>
          Bold Weight
        </Title>
        <Title as='h2' size='xl' thickness='extrabold'>
          Extra Bold Weight
        </Title>
        <Title as='h2' size='xl' thickness='black'>
          Black Weight
        </Title>
      </section>
      <section className='space-y-4'>
        <h2 className={clsx('mb-4', 'text-2xl', 'font-bold')}>All Decoration Variants</h2>
        <Title as='h2' size='xl' decoration='none'>
          No Decoration
        </Title>
        <Title as='h2' size='xl' decoration='underline'>
          Underlined Text
        </Title>
        <Title as='h2' size='xl' decoration='lineThrough'>
          Line Through Text
        </Title>
      </section>
      <section className='space-y-4'>
        <h2 className={clsx('mb-4', 'text-2xl', 'font-bold')}>All Color Variants</h2>
        <div className='rounded bg-gray-100 p-4'>
          <Title as='h2' size='xl' color='black'>
            Black Text
          </Title>
        </div>
        <div className='rounded bg-gray-800 p-4'>
          <Title as='h2' size='xl' color='white'>
            White Text
          </Title>
        </div>
      </section>
      <section className='space-y-4'>
        <h2 className={clsx('mb-4', 'text-2xl', 'font-bold')}>Complex Combinations</h2>
        <Title as='h1' size='3xl' thickness='extrabold' decoration='underline'>
          Large Bold Underlined Title
        </Title>
        <Title as='h2' size='2xl' thickness='light' decoration='lineThrough'>
          Medium Light Strikethrough Title
        </Title>
        <div className='rounded bg-gray-900 p-4'>
          <Title as='h3' size='xl' thickness='semibold' color='white' decoration='underline'>
            White Semibold Underlined on Dark
          </Title>
        </div>
        <Title as='h4' size='lg' thickness='thin' decoration='none'>
          Thin Regular Title
        </Title>
        <Title as='h5' size='md' thickness='black' decoration='underline'>
          Black Weight Medium Underlined
        </Title>
        <Title as='h6' size='sm' thickness='extralight'>
          Extra Light Small Title
        </Title>
      </section>
    </div>
  )
}
