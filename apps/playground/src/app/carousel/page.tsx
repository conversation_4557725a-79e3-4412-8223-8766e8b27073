/** biome-ignore-all lint/performance/noImgElement: Don't use <img> element */

import { Carousel } from '@venture-vibe/components/shared'

export default function CarouselComponents() {
  const newsImages = [
    'https://framerusercontent.com/images/a7RRSIPUBGKe4Qv07YgM40b0A8.jpg?scale-down-to=512',
    'https://framerusercontent.com/images/cofLlvrdf71d7i5raChitGnKzB0.jpg?scale-down-to=512',
    'https://framerusercontent.com/images/lNa3hddyXqAvfGMkbpBSZwkyZ8.jpg?scale-down-to=512',
    'https://framerusercontent.com/images/bHuCAjmPviqstnrXPcTP812Fg.jpg?scale-down-to=512',
    'https://framerusercontent.com/images/zwfc0qybZyVl0f6oiio5IcjOk.jpg?scale-down-to=512',
    'https://framerusercontent.com/images/0qM9Ztm4HZhZHt8vNXGofUBQf2w.jpg?scale-down-to=512',
    'https://framerusercontent.com/images/euwhQMJlIsBUxU8Hh7pkc31gYe0.jpg?scale-down-to=512',
    'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSK7u7tEFXHrkiVBpeMyxRhMIPsyquJYRzUvg&s',
  ]

  return (
    <div className='space-y-12 mx-auto'>
      {/* 1. Resimler için - Preview dots */}
      <Carousel loop autoplay dotsVariant='preview'>
        {newsImages.map(src => (
          // biome-ignore lint/nursery/useImageSize: Missing width or height attribute on img element.
          <img key={src} src={src} alt='Slide' className='w-full object-cover' />
        ))}
      </Carousel>

      {/* 2. Kartlar için - Normal dots */}
      <Carousel loop autoplay={false} dotsVariant='dots'>
        {newsImages.map((src, idx) => (
          <div key={src} className=' flex items-center justify-center bg-gray-900'>
            {/** biome-ignore lint/nursery/useImageSize: Missing width or height attribute on img element. */}
            <img src={src} alt={`Card ${idx + 1}`} className='w-full h-full object-cover rounded-lg' />
          </div>
        ))}
      </Carousel>

      {/* 3. Karışık içerik - preview dots */}
      <Carousel loop autoplay autoplayDelay={5000} dotsVariant='preview'>
        {newsImages.map((src, idx) => (
          // biome-ignore lint/nursery/useImageSize: Missing width or height attribute on img element.biomelint/nursery/useImageSize
          <img key={src} src={src} alt={`Mixed ${idx + 1}`} className='w-full  object-cover' />
        ))}
      </Carousel>

      {/* 4. Sadece navigation okları - dots olmadan */}
      <Carousel loop showArrows showDots={false}>
        {newsImages.map((src, idx) => (
          // biome-ignore lint/nursery/useImageSize: Missing width or height attribute on img element.
          <img key={src} src={src} alt={`Arrow Slide ${idx + 1}`} className='w-full  object-cover' />
        ))}
      </Carousel>

      {/* 5. Özelleştirilmiş dots stilleri */}
      <Carousel loop dotsVariant='preview' dotsClassName='mt-8' dotsPreviewClassName='w-32 h-20 rounded-lg'>
        {newsImages.map((src, idx) => (
          // biome-ignore lint/nursery/useImageSize: Missing width or height attribute on img element.
          <img key={src} src={src} alt={`Custom ${idx + 1}`} className='w-full  object-cover' />
        ))}
      </Carousel>
    </div>
  )
}
