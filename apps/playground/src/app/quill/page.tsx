import { type Delta, Quill<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Text, Title } from '@venture-vibe/components/shared'

// This is a server component, no 'use client' needed.

const sampleDeltaContent = {
  ops: [
    { insert: 'QuillJS Zengin Metin Örneği\n', attributes: { header: 1 } },

    { insert: 'Bu bir ' },
    { insert: 'kalın', attributes: { bold: true } },
    { insert: ', ' },
    { insert: 'italik', attributes: { italic: true } },
    { insert: ' ve ' },
    { insert: 'altı çizili', attributes: { underline: true } },
    { insert: ' metindir.\n' },

    { insert: 'Renkli metin', attributes: { color: '#e11d48' } },
    { insert: ' ve ' },
    { insert: 'arkaplan vurgusu', attributes: { background: '#fef08a' } },
    { insert: ' örneği. ' },
    { insert: 'Üst', attributes: { script: 'super' as const } },
    { insert: ' ve ' },
    { insert: 'alt', attributes: { script: 'sub' as const } },
    { insert: ' indisler.\n' },

    { insert: 'Ortalanmış bir satır\n', attributes: { align: 'center' } },

    { insert: 'Bağlantı örneği: ' },
    { insert: 'example.com', attributes: { link: 'https://example.com' } },
    { insert: '\n' },

    { insert: 'Ön Koşullar\n', attributes: { header: 2 } },
    { insert: 'Node.js 18+\n', attributes: { list: 'bullet' } },
    { insert: 'pnpm veya npm\n', attributes: { list: 'bullet' } },
    { insert: 'Tarayıcı (Chrome/Firefox)\n', attributes: { list: 'bullet' } },

    { insert: 'Kurulum Adımları\n', attributes: { header: 2 } },
    { insert: 'Depoyu klonlayın\n', attributes: { list: 'ordered' } },
    { insert: 'Bağımlılıkları yükleyin\n', attributes: { list: 'ordered' } },
    { insert: 'Geliştirme sunucusunu çalıştırın\n', attributes: { list: 'ordered' } },

    { insert: 'İleri Düzey İpuçları\n', attributes: { header: 3 } },
    { insert: 'Performans optimizasyonları\n', attributes: { list: 'bullet' } },
    { insert: 'Resimlerin gecikmeli yüklenmesi\n', attributes: { list: 'bullet', indent: 1 } },
    { insert: 'Kod bölme\n', attributes: { list: 'bullet', indent: 1 } },
    { insert: 'Önbellekleme stratejileri\n', attributes: { list: 'bullet' } },

    { insert: 'Alıntı örneği:\n' },
    { insert: 'Kullanıcı deneyimi küçük detaylarda kazanılır.\n', attributes: { blockquote: true } },

    { insert: 'Kod Bloğu\n', attributes: { header: 3 } },
    { insert: 'pnpm i\npnpm dev --host\n', attributes: { 'code-block': true } },

    { insert: 'Matematik Formülü:\n' },
    { insert: { formula: 'c = \\pm\\sqrt{a^2 + b^2}' } },
    { insert: '\n' },

    { insert: 'Görsel Örnek:\n' },
    { insert: { image: 'https://framerusercontent.com/images/8zvtKVafLdQFjCL5Ahk2ihCaTk.jpg?scale-down-to=512' } },
    { insert: '\n' },

    { insert: 'Video Örneği:\n' },
    { insert: { video: 'https://www.youtube.com/embed/dQw4w9WgXcQ' } },
    { insert: '\n' },

    { insert: 'Sağa hizalı bir kapanış cümlesi.\n', attributes: { align: 'right' } },

    { insert: 'Dipnot: ' },
    { insert: 'Bu içerik sahte veridir.', attributes: { italic: true, color: '#64748b' } },
    { insert: '\n' },
  ],
} satisfies Delta

export default function QuillDemoPage() {
  return (
    <div className='container mx-auto p-4'>
      <Title as='h1' preset='h1' className='mb-6'>
        Quill Delta Renderer Playground
      </Title>
      <Text as='p' className='mb-4'>
        Below is the content rendered from a sample Quill Delta JSON object:
      </Text>
      <div className='border p-4 rounded-lg bg-gray-50'>
        <QuillDeltaRenderer delta={sampleDeltaContent} />
      </div>
      <Text as='p' className='mt-8 text-gray-600'>
        End of demo content.
      </Text>
    </div>
  )
}
