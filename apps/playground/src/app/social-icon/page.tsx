import { SocialIcon } from '@venture-vibe/components/shared'
import { FacebookIcon, InstagramIcon, XIcon, YoutubeIcon } from '@venture-vibe/icons'
import { clsx } from '@venture-vibe/utils'

export default function FaqPage() {
  return (
    <div className={clsx('flex min-h-screen w-screen flex-col items-center justify-center', 'space-y-8 p-4', 'bg-bg')}>
      <div className={clsx('flex flex-rpw items-center justify-center', ' bg-dark p-20 rounded-max', 'gap-8')}>
        <SocialIcon icon={<FacebookIcon />} />
        <SocialIcon icon={<XIcon />} />
        <SocialIcon icon={<InstagramIcon />} />
        <SocialIcon icon={<YoutubeIcon />} />
        <SocialIcon size='sm' icon={<FacebookIcon />} />
        <SocialIcon size='sm' icon={<XIcon />} />
        <SocialIcon size='sm' icon={<InstagramIcon />} />
        <SocialIcon size='sm' icon={<YoutubeIcon />} />
        <SocialIcon color='secondary' icon={<FacebookIcon />} />
        <SocialIcon color='secondary' icon={<XIcon />} />
        <SocialIcon color='secondary' icon={<InstagramIcon />} />
        <SocialIcon color='secondary' icon={<YoutubeIcon />} />
        <SocialIcon color='secondary' size='sm' icon={<FacebookIcon />} />
        <SocialIcon color='secondary' size='sm' icon={<XIcon />} />
        <SocialIcon color='secondary' size='sm' icon={<InstagramIcon />} />
        <SocialIcon color='secondary' size='sm' icon={<YoutubeIcon />} />
      </div>
    </div>
  )
}
