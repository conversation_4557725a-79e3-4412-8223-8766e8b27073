import { Faq, type FaqItem } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'

const faqData: FaqItem[] = [
  {
    id: 'pricing',
    question: 'What is your pricing model?',
    answer:
      'We offer flexible pricing plans to suit different needs. Our basic plan starts at $29/month for small teams, with premium features available in our professional ($99/month) and enterprise plans. All plans include a 14-day free trial.',
  },
  {
    id: 'support',
    question: 'What kind of support do you provide?',
    answer:
      'We provide 24/7 customer support through multiple channels including email, live chat, and phone support. Our premium customers also get access to dedicated account managers and priority support.',
  },
  {
    id: 'integration',
    question: 'Does it integrate with existing tools?',
    answer:
      'Yes! Our platform integrates with over 100+ popular tools including Slack, Trello, Asana, Google Workspace, Microsoft 365, Salesforce, and many more. We also provide REST APIs for custom integrations.',
  },
  {
    id: 'security',
    question: 'How secure is your platform?',
    answer:
      'Security is our top priority. We use enterprise-grade encryption, SOC 2 Type II compliance, GDPR compliance, and regular security audits. All data is encrypted both in transit and at rest, and we maintain strict access controls.',
  },
  {
    id: 'setup',
    question: 'How long does it take to set up?',
    answer:
      'Most customers can get up and running in under 30 minutes. Our guided onboarding process walks you through the initial setup, and our support team is available to help with any questions during the process.',
  },
  {
    id: 'customization',
    question: 'Can I customize the platform to fit my needs?',
    answer:
      'Absolutely! Our platform offers extensive customization options including custom workflows, branded interfaces, configurable dashboards, and custom fields. Enterprise customers can also request specific feature development.',
  },
]

export default function FaqPage() {
  return (
    <div className={clsx('flex min-h-screen w-screen flex-col items-center justify-center', 'space-y-8 p-4', 'bg-bg')}>
      <div className={clsx('flex flex-col items-center justify-center', 'w-200', 'space-y-8')}>
        <Faq items={faqData} type='single' collapsible={true} color='light' className='w-full' />
      </div>
    </div>
  )
}
