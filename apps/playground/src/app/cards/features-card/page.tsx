/** biome-ignore-all lint/nursery/useSortedClasses: <immediate> */
/** biome-ignore-all lint/nursery/useImageSize: <immediate> */
/** biome-ignore-all lint/performance/noImgElement: <immediate> */
/** biome-ignore-all lint/a11y/useAltText: <immediate> */
import { FeaturesCard, type FeaturesCardButtonInfo, type FeaturesCardProps } from '@venture-vibe/components/shared'

export default function Home() {
  const buttonInfo: FeaturesCardButtonInfo = {
    text: 'Explore All',
    href: '',
  }

  const primary: FeaturesCardProps = {
    title: 'Templates',
    text: 'Our Premium templates are designed to accelerate your website creation process while offering complete flexibility. ',
    buttonInfo,
  }

  const secondary: FeaturesCardProps = {
    title: 'Components',
    text: 'Our exlusive components are designed to accelerate your website creation process while offering complete flexibility. ',
    color: 'secondary',
    buttonInfo,
  }

  const primaryAttachment = (
    <div className='absolute flex justify-center w-[66%] h-fit top-[178px] bottom-[-143px] left-[322px] right-[-111px] gap-5.5 transform -rotate-[28.96deg]'>
      <div className='relative flex flex-col items-center w-[49%] h-fit gap-5.5'>
        <img
          src='https://framerusercontent.com/images/CZxeGZWHPg75yyf5iQqo8QTJ6k.jpg?scale-down-to=512'
          className='aspect-w-full aspect-h-[161px] rounded-[0.75rem]'
        />
        <img
          src='https://framerusercontent.com/images/VmhZcWuvQz5BQkuy4FMvesYPc.jpg?scale-down-to=512'
          className='aspect-w-full aspect-h-[225px] rounded-[0.75rem]'
        />
      </div>
      <div className='relative flex flex-col items-center w-[49%] h-fit gap-5.5'>
        <img
          src='https://framerusercontent.com/images/nnx13KKv2KRA6KfBQO2gu0ouc.jpg?scale-down-to=512'
          className='aspect-w-full aspect-h-[200px] rounded-[0.75rem]'
        />
        <img
          src='https://framerusercontent.com/images/FOGekYUAxhecheO1AdRQhPwmF9c.jpg?scale-down-to=512'
          className='aspect-w-full aspect-h-[180px] rounded-[0.75rem]'
        />
      </div>
    </div>
  )

  const secondaryAttachment = (
    <div className='absolute flex justify-center w-[66%] h-fit top-[178px] bottom-[-143px] left-[322px] right-[-111px] gap-5.5 transform -rotate-[28.96deg]'>
      <div className='relative flex flex-col items-center w-[49%] h-fit gap-5.5'>
        <img
          src='https://framerusercontent.com/images/CZxeGZWHPg75yyf5iQqo8QTJ6k.jpg?scale-down-to=512'
          className='aspect-w-full aspect-h-[161px] rounded-[0.75rem]'
        />
        <img
          src='https://framerusercontent.com/images/VmhZcWuvQz5BQkuy4FMvesYPc.jpg?scale-down-to=512'
          className='aspect-w-full aspect-h-[225px] rounded-[0.75rem]'
        />
      </div>
      <div className='relative flex flex-col items-center w-[49%] h-fit gap-5.5'>
        <img
          src='https://framerusercontent.com/images/nnx13KKv2KRA6KfBQO2gu0ouc.jpg?scale-down-to=512'
          className='aspect-w-full aspect-h-[200px] rounded-[0.75rem]'
        />
        <img
          src='https://framerusercontent.com/images/FOGekYUAxhecheO1AdRQhPwmF9c.jpg?scale-down-to=512'
          className='aspect-w-full aspect-h-[180px] rounded-[0.75rem]'
        />
      </div>
    </div>
  )

  return (
    <div className='flex h-screen flex-col gap-16 bg-[#ffffff] p-16'>
      <FeaturesCard {...primary} size='lg' attachment={primaryAttachment} />
      <FeaturesCard {...secondary} size='lg' attachment={secondaryAttachment} />
      <FeaturesCard {...primary} size='md' />
      <FeaturesCard {...secondary} size='md' />
      <FeaturesCard {...primary} size='sm' />
      <FeaturesCard {...secondary} size='sm' />
    </div>
  )
}
