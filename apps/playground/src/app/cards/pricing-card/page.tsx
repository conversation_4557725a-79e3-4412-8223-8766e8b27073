import { Button, PricingCard } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'

export default function PricingCardPage() {
  const pricingPlans = [
    {
      planType: 'Basic',
      description: 'Free plan for basic users',
      features: [
        'Access to 5 free templates',
        'Basic customization',
        '3 downloads a day',
        'Community support',
        'Basic templates',
      ],
      buttonColor: 'outlineBlack' as const,
      color: 'primary' as const,
    },
    {
      planType: 'Standart',
      description: 'Growing Team & Mid-sized',
      price: 29,
      features: [
        'Access to 30 templates',
        'Advanced customization',
        '10 downloads a day',
        'Priority support',
        'Premium templates',
      ],
      discount: 35,
      buttonColor: 'black' as const,
      color: 'secondary' as const,
    },
    {
      planType: 'Enterprise',
      description: 'Large Projects & Organizations',
      price: 49,
      features: [
        'Full access to all templates',
        'Advanced customization',
        'Unlimited downloads',
        'Dedicated support 24/7',
        'High-end templates',
      ],
      discount: 40,
      buttonColor: 'outlineBlack' as const,
      color: 'tertiary' as const,
    },
  ]

  return (
    <div className={clsx('flex min-h-screen w-screen flex-col items-center justify-center', 'space-y-8 p-4', 'bg-bg')}>
      <div className={clsx('flex flex-row items-center justify-center', 'w-full max-w-7xl', 'space-x-8')}>
        {pricingPlans.map(plan => (
          <PricingCard
            key={plan.planType}
            planType={plan.planType}
            description={plan.description}
            price={plan.price}
            size='primary'
            features={plan.features}
            discount={plan.discount}
            color={plan.color}
            button={
              <Button color={plan.buttonColor} size='lg' className='w-full'>
                Choose Your Plan
              </Button>
            }
          />
        ))}
      </div>
    </div>
  )
}
