import { TestimonialCard } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'

export default function TestimonialCardPage() {
  return (
    <div className={clsx('flex min-h-screen w-screen flex-col items-center justify-center', 'space-y-8 p-4', 'bg-bg')}>
      <div className={clsx('flex flex-row items-center justify-center', 'w-full max-full', 'space-x-8')}>
        <TestimonialCard
          name='<PERSON>'
          position='CEO, Company'
          logo={
            <div
              className={clsx('h-12 w-64', 'bg-contain', 'bg-center')}
              style={{ backgroundImage: 'url(https://framerusercontent.com/images/QZDGJrEBgtlcDQzUZ4qH9SSso2g.png)' }}
            />
          }>
          This is a great product! It has changed the way we do business.This is a great product! It has changed the way
          we do business.This is a great product! It has changed the way we do business.
        </TestimonialCard>
      </div>
      <TestimonialCard
        name='<PERSON>'
        position='CTO, Another Company'
        logo={
          <div
            className={clsx('h-12 w-64', 'bg-contain', 'bg-center')}
            style={{ backgroundImage: 'url(https://framerusercontent.com/images/QZDGJrEBgtlcDQzUZ4qH9SSso2g.png)' }}
          />
        }>
        I love using this service. It has made my life so much easier!
      </TestimonialCard>
    </div>
  )
}
