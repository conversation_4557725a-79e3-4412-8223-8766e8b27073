import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'

export default function CardPage() {
  const variants = ['primary', 'secondary', 'tertiary', 'quaternary'] as const

  const images = [
    'https://framerusercontent.com/images/8zvtKVafLdQFjCL5Ahk2ihCaTk.jpg?scale-down-to=512',
    'https://framerusercontent.com/images/nnx13KKv2KRA6KfBQO2gu0ouc.jpg?scale-down-to=512',
    'https://framerusercontent.com/images/GDQ92AvHGUYCmiszN6bF8KeHU.jpg?scale-down-to=512',
    'https://framerusercontent.com/images/FVs3ZZOva62DkhveRLVlJN87g.jpg?scale-down-to=512',
    'https://framerusercontent.com/images/VmhZcWuvQz5BQkuy4FMvesYPc.jpg?scale-down-to=512',
  ]

  const commonProps = {
    badge: <Badge variant='primary-accent' text='Pro' size='xs' />,
    title: 'Card Title',
    description: 'This is a sample card description. It provides a brief overview of the card content.',
  }

  const hoverComponent = (buttonColor = 'secondary') => (
    <div className={clsx('flex flex-row items-center justify-between', 'w-full', 'space-x-4')}>
      <Button color={buttonColor as 'secondary' | 'primary' | 'black'} size='sm'>
        Activate
      </Button>
      <Button color='outlineWhite' size='sm'>
        Copy
      </Button>
    </div>
  )

  return (
    <div className={clsx('flex min-h-screen w-screen flex-col items-center justify-center', 'space-y-8 p-4', 'bg-bg')}>
      {/* Primary type cards - small size */}
      <div className={clsx('flex flex-row items-center justify-center', 'w-full max-w-7xl', 'space-x-8')}>
        {variants.map(variant => (
          <Card
            key={`primary-sm-${variant}`}
            type='primary'
            variant={variant}
            tags={['deneme', 'test', 'örnek']}
            date='20.10.2023'
            writer='John Doe'
            image={images[0]}
            {...commonProps}
          />
        ))}
      </div>

      {/* Primary type cards - large size */}
      <div className={clsx('flex flex-row items-center justify-center', 'w-full max-w-7xl', 'space-x-8')}>
        {variants.map(variant => (
          <Card
            key={`primary-lg-${variant}`}
            type='primary'
            variant={variant}
            tags={['deneme', 'test', 'örnek']}
            date='20.10.2023'
            writer='John Doe'
            image={images[1]}
            {...commonProps}
          />
        ))}
      </div>

      {/* Secondary type cards - small size with hover components */}
      <div className={clsx('flex flex-row items-center justify-center', 'w-full max-w-7xl', 'space-x-8')}>
        {variants.map(variant => {
          const buttonColor = variant === 'secondary' ? 'primary' : variant === 'quaternary' ? 'black' : 'secondary'
          return (
            <Card
              key={`secondary-sm-${variant}`}
              type='secondary'
              variant={variant}
              hoverComponent={hoverComponent(buttonColor)}
              image={images[2]}
              {...commonProps}
            />
          )
        })}
      </div>
      {/* Secondary type cards - large size with hover components */}
      <div className={clsx('flex flex-row items-center justify-center', 'w-full max-w-7xl', 'space-x-8')}>
        {variants.map(variant => {
          const buttonColor = variant === 'secondary' ? 'primary' : variant === 'quaternary' ? 'black' : 'secondary'
          return (
            <Card
              key={`secondary-lg-${variant}`}
              type='secondary'
              variant={variant}
              hoverComponent={hoverComponent(buttonColor)}
              image={images[3]}
              {...commonProps}
            />
          )
        })}
      </div>
      {/* Secondary type cards - small size without texts */}
      <div className={clsx('flex flex-row items-center justify-center', 'w-full max-w-7xl', 'space-x-8')}>
        {variants.map(variant => {
          const buttonColor = variant === 'secondary' ? 'primary' : variant === 'quaternary' ? 'black' : 'secondary'
          return (
            <Card
              key={`secondary-sm-no-text-${variant}`}
              type='secondary'
              variant={variant}
              showTexts={false}
              hoverComponent={hoverComponent(buttonColor)}
              image={images[4]}
              {...commonProps}
            />
          )
        })}
      </div>
      {/* Secondary type cards - large size without texts */}
      <div className={clsx('flex flex-row items-center justify-center', 'w-full max-w-7xl', 'space-x-8')}>
        {variants.map(variant => {
          const buttonColor = variant === 'secondary' ? 'primary' : variant === 'quaternary' ? 'black' : 'secondary'
          return (
            <Card
              key={`secondary-lg-no-text-${variant}`}
              type='secondary'
              variant={variant}
              showTexts={false}
              hoverComponent={hoverComponent(buttonColor)}
              image={images[0]}
              {...commonProps}
            />
          )
        })}
      </div>
    </div>
  )
}
