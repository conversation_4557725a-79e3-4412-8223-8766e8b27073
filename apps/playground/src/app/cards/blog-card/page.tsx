import { Badge, BlogCard } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'

export default function BlogCardPage() {
  return (
    <div className={clsx('flex min-h-screen w-screen flex-col items-center justify-center', 'space-y-8 p-4', 'bg-bg')}>
      <div className={clsx('flex flex-row items-center justify-center', 'w-full max-w-7xl', 'space-x-8')}>
        <BlogCard
          type='primary'
          title='Top 5 Animation Tips to Make you an Motion Wizard'
          description='Famskey is a powerful tool for creating smooth, interactive animations that can elevate the user experience of any website.'
          link='https://example.com/blog-post'
          image='https://framerusercontent.com/images/8zvtKVafLdQFjCL5Ahk2ihCaTk.jpg?scale-down-to=512'
          date='January 1, 2023'
          badge={
            <div className={clsx('flex flex-row items-center', 'space-x-4')}>
              <Badge variant='danger' text='New' size='sm' />
              <Badge variant='primary-accent' text='Featured' size='sm' />
            </div>
          }
        />
        <BlogCard
          type='primary'
          title='Top 5 Animation Tips to Make you an Motion Wizard'
          variant='secondary'
          description='Famskey is a powerful tool for creating smooth, interactive animations that can elevate the user experience of any website.'
          link='https://example.com/blog-post'
          image='https://framerusercontent.com/images/8zvtKVafLdQFjCL5Ahk2ihCaTk.jpg?scale-down-to=512'
          date='January 1, 2023'
          badge={
            <div className={clsx('flex flex-row items-center', 'space-x-4')}>
              <Badge variant='danger' text='New' size='sm' />
              <Badge variant='primary-accent' text='Featured' size='sm' />
            </div>
          }
        />
        <BlogCard
          type='primary'
          title='Top 5 Animation Tips to Make you an Motion Wizard'
          variant='tertiary'
          description='Famskey is a powerful tool for creating smooth, interactive animations that can elevate the user experience of any website.'
          link='https://example.com/blog-post'
          image='https://framerusercontent.com/images/8zvtKVafLdQFjCL5Ahk2ihCaTk.jpg?scale-down-to=512'
          date='January 1, 2023'
          badge={
            <div className={clsx('flex flex-row items-center', 'space-x-4')}>
              <Badge variant='danger' text='New' size='sm' />
              <Badge variant='primary-accent' text='Featured' size='sm' />
            </div>
          }
        />
        <BlogCard
          type='primary'
          title='Top 5 Animation Tips to Make you an Motion Wizard'
          variant='quaternary'
          description='Famskey is a powerful tool for creating smooth, interactive animations that can elevate the user experience of any website.'
          link='https://example.com/blog-post'
          image='https://framerusercontent.com/images/8zvtKVafLdQFjCL5Ahk2ihCaTk.jpg?scale-down-to=512'
          date='January 1, 2023'
          badge={<Badge variant='danger' text='New' size='sm' />}
        />
      </div>
      <div className={clsx('flex flex-row items-center justify-center', 'w-full', 'space-x-8')}>
        <BlogCard
          type='primary'
          title='Top 5 Animation Tips to Make you an Motion Wizard'
          description='Famskey is a powerful tool for creating smooth, interactive animations that can elevate the user experience of any website. Whether you’re new to Framer or looking to fine-tune your skills, these top 10 animation tips will help you become a master at using tools to create dynamic, engaging designs.'
          link='https://example.com/blog-post'
          image='https://framerusercontent.com/images/8zvtKVafLdQFjCL5Ahk2ihCaTk.jpg?scale-down-to=512'
          date='January 1, 2023'
          badge={
            <div className={clsx('flex flex-row items-center', 'space-x-4')}>
              <Badge variant='danger' text='New' size='sm' />
              <Badge variant='primary-accent' text='Featured' size='sm' />
            </div>
          }
        />
      </div>
      <div className={clsx('flex flex-row items-center justify-center', 'w-full', 'space-x-8')}>
        <BlogCard
          type='primary'
          variant='secondary'
          title='Top 5 Animation Tips to Make you an Motion Wizard'
          description='Famskey is a powerful tool for creating smooth, interactive animations that can elevate the user experience of any website. Whether you’re new to Framer or looking to fine-tune your skills, these top 10 animation tips will help you become a master at using tools to create dynamic, engaging designs.'
          link='https://example.com/blog-post'
          image='https://framerusercontent.com/images/8zvtKVafLdQFjCL5Ahk2ihCaTk.jpg?scale-down-to=512'
          date='January 1, 2023'
          badge={
            <div className={clsx('flex flex-row items-center', 'space-x-4')}>
              <Badge variant='danger' text='New' size='sm' />
              <Badge variant='primary-accent' text='Featured' size='sm' />
            </div>
          }
        />
      </div>
      <div className={clsx('flex flex-row items-center justify-center', 'w-full', 'space-x-8')}>
        <BlogCard
          type='primary'
          variant='tertiary'
          title='Top 5 Animation Tips to Make you an Motion Wizard'
          description='Famskey is a powerful tool for creating smooth, interactive animations that can elevate the user experience of any website. Whether you’re new to Framer or looking to fine-tune your skills, these top 10 animation tips will help you become a master at using tools to create dynamic, engaging designs.'
          link='https://example.com/blog-post'
          image='https://framerusercontent.com/images/8zvtKVafLdQFjCL5Ahk2ihCaTk.jpg?scale-down-to=512'
          date='January 1, 2023'
          badge={
            <div className={clsx('flex flex-row items-center', 'space-x-4')}>
              <Badge variant='danger' text='New' size='sm' />
              <Badge variant='primary-accent' text='Featured' size='sm' />
            </div>
          }
        />
      </div>
      <div className={clsx('flex flex-row items-center justify-center', 'w-full', 'space-x-8')}>
        <BlogCard
          type='primary'
          variant='quaternary'
          title='Top 5 Animation Tips to Make you an Motion Wizard'
          description='Famskey is a powerful tool for creating smooth, interactive animations that can elevate the user experience of any website. Whether you’re new to Framer or looking to fine-tune your skills, these top 10 animation tips will help you become a master at using tools to create dynamic, engaging designs.'
          link='https://example.com/blog-post'
          image='https://framerusercontent.com/images/8zvtKVafLdQFjCL5Ahk2ihCaTk.jpg?scale-down-to=512'
          date='January 1, 2023'
          badge={
            <div className={clsx('flex flex-row items-center', 'space-x-4')}>
              <Badge variant='danger' text='New' size='sm' />
              <Badge variant='primary-accent' text='Featured' size='sm' />
            </div>
          }
        />
      </div>
      <div className={clsx('flex flex-row items-center justify-center', 'w-full', 'space-x-8')}>
        <BlogCard
          type='secondary'
          variant='primary'
          title='Top 5 Animation Tips to Make you an Motion Wizard'
          description='Elements that fade or slide into view as users scroll create a layered and immersive experience. Such as rotating and fading an element at different points in an animation.'
          link='https://example.com/blog-post'
          image='https://framerusercontent.com/images/8zvtKVafLdQFjCL5Ahk2ihCaTk.jpg?scale-down-to=512'
        />
        <BlogCard
          type='secondary'
          variant='secondary'
          title='Top 5 Animation Tips to Make you an Motion Wizard'
          description='Elements that fade or slide into view as users scroll create a layered and immersive experience. Such as rotating and fading an element at different points in an animation.'
          link='https://example.com/blog-post'
          image='https://framerusercontent.com/images/8zvtKVafLdQFjCL5Ahk2ihCaTk.jpg?scale-down-to=512'
        />
        <BlogCard
          type='secondary'
          variant='tertiary'
          title='Top 5 Animation Tips to Make you an Motion Wizard'
          description='Elements that fade or slide into view as users scroll create a layered and immersive experience. Such as rotating and fading an element at different points in an animation.'
          link='https://example.com/blog-post'
          image='https://framerusercontent.com/images/8zvtKVafLdQFjCL5Ahk2ihCaTk.jpg?scale-down-to=512'
        />
        <BlogCard
          type='secondary'
          variant='quaternary'
          title='Top 5 Animation Tips to Make you an Motion Wizard'
          description='Elements that fade or slide into view as users scroll create a layered and immersive experience. Such as rotating and fading an element at different points in an animation.'
          link='https://example.com/blog-post'
          image='https://framerusercontent.com/images/8zvtKVafLdQFjCL5Ahk2ihCaTk.jpg?scale-down-to=512'
        />
      </div>
    </div>
  )
}
