import { CategoryCard } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'

const imageArray = [
  'https://framerusercontent.com/images/uMO7LeC9sL4DO8giZ8oMkhrqTyw.jpg?scale-down-to=512',
  'https://framerusercontent.com/images/6Bz9QSq6Nw8SZsV9zAGXjaM4ko.jpg?scale-down-to=512',
  'https://framerusercontent.com/images/TyC4pJ2H3x38IluZugMSYyWWQTg.jpg?scale-down-to=512',
  'https://framerusercontent.com/images/4eWbE7TpFYkurzXfq85RWfLaU.jpg?scale-down-to=512',
  'https://framerusercontent.com/images/8a3jOElysjcB1qyJTGss81qGKQ.jpg?scale-down-to=512',
]

export default function CategoryCardPage() {
  return (
    <div className={clsx('flex min-h-screen w-screen flex-col items-center justify-center', 'space-y-8 p-4', 'bg-bg')}>
      <div className={clsx('grid w-full max-w-7xl', 'gap-8', 'grid-cols-1 md:grid-cols-2')}>
        <CategoryCard
          title='Category 1'
          link='/playground/category-card'
          color='primary'
          imageUrl={imageArray}
          buttonText='View More'
        />
        <CategoryCard
          title='Category 2'
          link='/playground/category-card'
          color='secondary'
          imageUrl={imageArray}
          buttonText='View More'
        />
        <div className='max-w-600 flex flex-row gap-8'>
          <CategoryCard
            title='Category 3'
            link='/playground/category-card'
            customContent={true}
            content={
              <iframe
                className='w-full h-full min-h-60 md:min-h-96'
                src='https://www.youtube.com/embed/tgbNymZ7vqY'
                title='yt'
              />
            }
            buttonText='View More'
          />
        </div>
        <div className='max-w-600 flex flex-row gap-8'>
          <CategoryCard
            title='Category 4'
            link='/playground/category-card'
            customContent={true}
            content={
              <iframe
                className='w-full h-full min-h-60 md:min-h-96'
                src='https://www.youtube.com/embed/tgbNymZ7vqY'
                title='yt'
              />
            }
            buttonText='View More'
          />
        </div>
        <div className='max-w-400 flex flex-row gap-8'>
          <CategoryCard
            title='Category 5'
            link='/playground/category-card'
            imageUrl={imageArray}
            buttonText='View More'
          />
          <CategoryCard
            title='Category 6'
            link='/playground/category-card'
            color='secondary'
            imageUrl={imageArray}
            buttonText='View More'
          />
        </div>
      </div>
    </div>
  )
}
