import { Badge, ProjectCard } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'

export default function TestimonialCardPage() {
  return (
    <div className={clsx('flex min-h-screen w-screen flex-col items-center justify-center', 'space-y-8 p-4', 'bg-bg')}>
      <div className={clsx('flex flex-row items-center justify-center', 'w-full max-full', 'space-x-8')}>
        <ProjectCard
          title='Hizmet al, Ekibini guclendir.'
          description='Uzman tasarimlar, yazilimcilar ve danismanlarla calismaya basla'
          tags={['QA Tester', 'Art Director', 'Marketing Manager', 'UI/UX Designer']}
          badge={<Badge className='px-8' text='Hizmet Al' />}
          buttonText='Proje Yayinla'
          color='primary'
        />
      </div>
    </div>
  )
}
