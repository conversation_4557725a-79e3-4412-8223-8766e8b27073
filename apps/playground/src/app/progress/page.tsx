import { Progress } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'

export default function ProgressPage() {
  return (
    <div className={clsx('min-h-screen w-screen bg-bg p-8')}>
      <div className={clsx('mx-auto max-w-500 space-y-12')}>
        <div className={clsx('space-y-6')}>
          <div className={clsx('grid gap-6 sm:grid-cols-2')}>
            <Progress value={25} size='sm' color='primary' />
            <Progress value={75} size='sm' color='secondary' />
          </div>
        </div>
        <div className={clsx('space-y-6')}>
          <div className={clsx('grid gap-6 sm:grid-cols-2')}>
            <Progress value={40} size='md' color='primary' />
            <Progress value={85} size='md' color='secondary' />
          </div>
        </div>
        <div className={clsx('space-y-6')}>
          <div className={clsx('grid gap-6 sm:grid-cols-2')}>
            <Progress value={60} size='lg' color='primary' />
            <Progress value={90} size='lg' color='secondary' />
          </div>
        </div>
        <div className={clsx('space-y-6')}>
          <Progress value={45} size='lg' color='primary' indicator indicatorLocation='start' />
          <Progress value={65} size='lg' color='primary' indicator indicatorLocation='center' />
          <Progress value={85} size='lg' color='primary' indicator indicatorLocation='end' />
        </div>
        <div className={clsx('space-y-6')}>
          <Progress value={30} size='lg' color='secondary' indicator indicatorLocation='start' />
          <Progress value={60} size='lg' color='secondary' indicator indicatorLocation='center' />
          <Progress value={95} size='lg' color='secondary' indicator indicatorLocation='end' />
        </div>
        <div className={clsx('space-y-4')}>
          <Progress value={15} size='sm' color='primary' />
          <Progress value={35} size='md' color='secondary' />
          <Progress value={55} size='lg' color='primary' />
          <Progress value={75} size='md' color='secondary' indicator />
          <Progress value={100} size='lg' color='primary' indicator indicatorLocation='end' />
        </div>
        <div className={clsx('space-y-4')}>
          <Progress value={0} size='md' color='primary' />
          <Progress value={100} size='md' color='secondary' />
          <Progress value={50} size='sm' color='primary' indicator />
          <Progress value={100} size='sm' color='secondary' indicator />
        </div>
      </div>
    </div>
  )
}
