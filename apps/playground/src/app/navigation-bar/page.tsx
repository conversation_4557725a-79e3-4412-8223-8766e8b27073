/** biome-ignore-all lint/nursery/useImageSize: Reduntant */
/** biome-ignore-all lint/nursery/useSortedClasses: Reduntant */
/** biome-ignore-all lint/performance/noImgElement: Reduntant */
/** biome-ignore-all lint/a11y/useAltText: Reduntant */
'use client'

import { /** Button, */ HeroProvider, NavigationBar, type NavigationBarLink } from '@venture-vibe/components/shared'

export default function Home() {
  const logo = <img src='https://framerusercontent.com/images/gcqByUEogQ0BlZa8m7Hmg8E3th4.svg' />

  const links: NavigationBarLink[] = [
    {
      text: 'Templates',
      href: 'templates',
    },
    {
      text: 'Components',
      href: 'components',
    },
    {
      text: 'Pricing',
      href: 'pricing',
    },
    {
      text: 'Blog',
      href: 'blog',
    },
    {
      text: 'Contact Us',
      href: 'contact-us',
    },
    {
      text: 'Terms of Service',
      href: 'terms-of-service',
    },
    {
      text: '404',
      href: '404',
    },
  ]

  return (
    <div className='flex h-[500vh] bg-[#bababa] p-10'>
      <HeroProvider>
        <NavigationBar logo={logo} links={links} />
        {/* <div className='flex fixed top-1/2 inset-x-0'>
          <Button color='outlineBlack' className='mx-auto cursor-pointer'>
            Change Size
          </Button>
        </div> */}
      </HeroProvider>
    </div>
  )
}
