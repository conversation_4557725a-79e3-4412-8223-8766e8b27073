import { Button, Input, Text, TextArea, Title } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'

export default function CardPage() {
  return (
    <div className={clsx('flex min-h-screen w-screen flex-row items-center justify-center', 'space-x-8 p-4', 'bg-bg')}>
      <div className={clsx('flex flex-col items-center justify-center', 'w-300', 'space-y-8')}>
        <div className='bg-dark p-10 rounded-max gap-4'>
          <div className='flex flex-col items-center mb-8'>
            <Title as='h2' thickness='semibold' color='white'>
              Subscribe our Newsletter
            </Title>
          </div>
          <div className='flex flex-row gap-8'>
            <Input placeholder='Enter your email' size='lg' variant='secondary' roundness='max' />
            <Button color='primary' size='lg' className='w-80 text-md'>
              Subscribe
            </Button>
          </div>
        </div>
      </div>
      <div
        className={clsx(
          'flex flex-col items-center justify-center',
          'w-300',
          'space-y-8',
          'bg-white',
          'border border-platinum-stroke',
          'p-14',
          'rounded-[32px]',
        )}>
        <div className={clsx('flex flex-row gap-20', 'w-full')}>
          <div className={clsx('flex flex-col space-y-4', 'w-full')}>
            <Text as='label'>Full Name</Text>
            <Input placeholder='Enter your full name' size='lg' />
          </div>
          <div className={clsx('flex flex-col space-y-4', 'w-full')}>
            <Text as='label'>Email Address</Text>
            <Input placeholder='Email Address' size='lg' />
          </div>
        </div>
        <div className={clsx('flex flex-col space-y-4', 'w-full')}>
          <Text as='label'>Subject</Text>
          <Input placeholder='Subject' size='lg' />
        </div>
        <div className={clsx('flex flex-col space-y-4', 'w-full')}>
          <Text as='label'>Message</Text>
          <TextArea
            isResizable={false}
            placeholder='Write Something'
            className={clsx(
              'h-32',
              'border border-light-gray rounded-lg bg-white text-sm focus:ring-light-gray focus:border-light-gray',
            )}
          />
          <Button color='secondary' size='lg' className='w-80 mt-10 text-md'>
            Send Message
          </Button>
        </div>
      </div>
    </div>
  )
}
