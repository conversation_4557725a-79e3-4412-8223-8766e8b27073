import { GridFlexContainer } from '@venture-vibe/components/shared'

const GRID_COLUMNS = 6
export default function GridFlexContainerPage() {
  return (
    <main className='flex flex-col gap-16 p-16 min-h-screen'>
      <section>
        <h2 className='text-xl font-semibold mb-2'>Grid: 3 Columns, Gap md</h2>
        <GridFlexContainer layout='grid' columns={3} gap='md' className='bg-light-spring p-4 rounded'>
          <div className='bg-white p-6 shadow rounded'>Item 1</div>
          <div className='bg-white p-6 shadow rounded'>Item 2</div>
          <div className='bg-white p-6 shadow rounded'>Item 3</div>
        </GridFlexContainer>
      </section>
      <section>
        <h2 className='text-xl font-semibold mb-2'>Flex: Row, Gap lg</h2>
        <GridFlexContainer layout='flex' direction='row' gap='lg' className='bg-light-orange p-4 rounded'>
          <div className='bg-white p-6 shadow rounded'>Flex 1</div>
          <div className='bg-white p-6 shadow rounded'>Flex 2</div>
          <div className='bg-white p-6 shadow rounded'>Flex 3</div>
        </GridFlexContainer>
      </section>
      <section>
        <h2 className='text-xl font-semibold mb-2'>Grid: 6 Columns, Gap sm, Center Align</h2>
        <GridFlexContainer
          layout='grid'
          columns={GRID_COLUMNS}
          gap='sm'
          align='center'
          className='bg-light-orange p-4 rounded'>
          {[...new Array(GRID_COLUMNS)].map((_, i) => (
            <div key={`col-${i + 1}`} className='bg-white p-4 shadow rounded text-center'>
              Col {i + 1}
            </div>
          ))}
        </GridFlexContainer>
      </section>
      <section>
        <h2 className='text-xl font-semibold mb-2'>Flex: Column, Justify Center</h2>
        <GridFlexContainer
          layout='flex'
          direction='column'
          gap='md'
          justify='center'
          align='start'
          className='bg-dark-gray text-white p-4 rounded'>
          <GridFlexContainer layout='flex' direction='row' gap='md' className='w-full'>
            <div className='bg-orange p-4 shadow rounded'>Column 1</div>
            <div className='bg-orange p-4 shadow rounded'>Column 2</div>
          </GridFlexContainer>
          <div className='bg-light-spring p-4 shadow rounded'>Column 1</div>
          <div className='bg-light-spring p-4 shadow rounded'>Column 2</div>
        </GridFlexContainer>
      </section>
    </main>
  )
}
