import { Footer, SocialIcon } from '@venture-vibe/components/shared'
import { FacebookIcon, InstagramIcon, XIcon, YoutubeIcon } from '@venture-vibe/icons'
import { clsx } from '@venture-vibe/utils'

const test = [
  {
    title: 'Company',
    menu: [
      { title: 'Blog', href: '/a' },
      { title: 'Templates', href: '/b' },
      { title: 'Components', href: '/c' },
      { title: 'Pricing Plan', href: '/d' },
      { title: 'Contact us', href: '/e' },
    ],
  },
  {
    title: 'Support',
    menu: [
      { title: 'Terms of Service', href: '/f' },
      { title: 'Get Template', href: '/g' },
    ],
  },
]

export default function FaqPage() {
  return (
    <div className={clsx('flex min-h-screen w-full flex-col items-center justify-center', 'space-y-8', 'bg-bg')}>
      <div className='w-full h-500 bg-bg'>ok</div>
      <Footer
        iconUrl='https://framerusercontent.com/images/RaombZmzGME8gmXeBluxy6vD7I.svg'
        title='Subscribe Our Newsletter'
        description='Create profiles, connect with friends, or build networks & community.'
        menuItems={test}
        bottomLeftText='HubStore All Rights Reserved.'
        bottomRightText='© 2025 Legal'
        socialIcons={
          <div className='flex flex-row gap-8'>
            <SocialIcon icon={<FacebookIcon />} href='https://facebook.com' />
            <SocialIcon icon={<XIcon />} href='https://x.com' />
            <SocialIcon icon={<InstagramIcon />} href='https://instagram.com' />
            <SocialIcon icon={<YoutubeIcon />} href='https://youtube.com' />
          </div>
        }
        socialTitle='Follow Us'
        buttonText='Subscribe'
      />
    </div>
  )
}
