import { StatisticWidget } from '@venture-vibe/components/shared'
import { ApprovalTickIcon, InterfaceIcon, TwoPeopleIcon } from '@venture-vibe/icons'

export default function Home() {
  const approvalTickIcon = <ApprovalTickIcon width={28} height={28} color='#FF7C33' />
  const twoPeopleIcon = <TwoPeopleIcon width={28} height={28} color='#80A90A' />
  const interfaceIcon = <InterfaceIcon width={28} height={28} color='#7521FB' />

  return (
    <div className='flex min-h-screen items-center justify-center'>
      <div className='grid grid-cols-3 gap-32'>
        <div className='grid-rows-3 grid gap-32'>
          <StatisticWidget title='24+ Templates' icon={approvalTickIcon}>
            Free & premium assets to showcase a large collection of design-ready templates for all use cases.
          </StatisticWidget>
          <StatisticWidget title='50,000+ Satisfied' icon={twoPeopleIcon}>
            Designers & Developers – Join a thriving community of creative professionals.
          </StatisticWidget>
          <StatisticWidget title='10,000+ UI Components' icon={interfaceIcon}>
            Highlight the number of UI components that users can access and customize. Up your design agency.
          </StatisticWidget>
        </div>
      </div>
    </div>
  )
}
