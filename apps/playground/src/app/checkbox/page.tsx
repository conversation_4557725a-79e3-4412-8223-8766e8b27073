'use client'

import { Checkbox } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'

export default function Home() {
  const variants = ['primary', 'secondary'] as const
  const sizes = ['sm', 'md', 'lg'] as const
  const roundnesses = ['sm', 'md', 'lg', 'max'] as const

  return (
    <div className={clsx('flex min-h-screen w-screen flex-col items-center justify-center bg-bg', 'space-y-8 p-8')}>
      {variants.map(variant => (
        <div key={variant} className='max-w-5xl space-y-4'>
          {sizes.map(size => (
            <div key={size} className='space-y-2'>
              <div className='flex flex-wrap items-center gap-4'>
                {roundnesses.map(roundness => (
                  <Checkbox
                    key={roundness}
                    variant={variant}
                    size={size}
                    roundness={roundness}
                    label={`${variant} ${size} ${roundness}`}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      ))}
      <div className='max-w-5xl space-y-4'>
        <div className='flex flex-wrap items-center gap-4'>
          <Checkbox label='Disabled' />
          <Checkbox checked label='Checked Disabled' />
        </div>
      </div>
    </div>
  )
}
