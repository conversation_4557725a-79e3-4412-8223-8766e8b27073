'use client'

import { Button } from '@venture-vibe/components/shared'
import { ArrowRight, Plus } from '@venture-vibe/icons'
import { clsx } from '@venture-vibe/utils'

export default function Home() {
  const colors = ['primary', 'secondary', 'outlineBlack', 'outlineWhite', 'error', 'black'] as const
  const sizes = ['lg', 'sm'] as const

  return (
    <div className={clsx('flex min-h-screen w-screen flex-col items-center justify-center bg-bg', 'space-y-8 p-8')}>
      {colors.map(color => (
        <div key={color} className='max-w-5xl space-y-4'>
          {sizes.map(size => (
            <div key={size} className='space-y-2'>
              <div className='flex flex-wrap items-center gap-4'>
                <Button color={color} size={size}>
                  Default
                </Button>
                <Button color={color} size={size} icon={<Plus />}>
                  Icon Left
                </Button>
                <Button color={color} size={size} icon={<ArrowRight />} iconPosition='right'>
                  Icon Right
                </Button>
                <Button color={color} size={size} icon={<Plus />} />
                <Button color={color} size={size} loading />
                <Button color={color} size={size} loading showLoadingText>
                  Loading
                </Button>
                <Button color={color} size={size} disabled>
                  Disabled
                </Button>
                <Button color={color} size={size} icon={<Plus />} disabled>
                  Icon Disabled
                </Button>
              </div>
            </div>
          ))}
        </div>
      ))}
      <div className='max-w-5xl space-y-4'>
        <div className='flex flex-wrap items-center gap-4'>
          <Button type='submit'>Type Submit</Button>
          <Button color='unstyled' className='border-4 border-danger bg-bright-orange'>
            Custom Class
          </Button>
          <Button
            color='primary'
            icon={<Plus />}
            iconClassName='transition-transform duration-300 group-hover:rotate-45'
            className='group'>
            Hover'da Döner
          </Button>
          <Button
            color='primary'
            icon={<ArrowRight />}
            className='group'
            iconClassName='transition-transform duration-300 -rotate-45 group-hover:rotate-0'
            iconPosition='right'>
            Animated Icon
          </Button>
        </div>
      </div>
    </div>
  )
}
