import { Badge } from '@venture-vibe/components/shared'
import { ArrowRight, MagnifyingGlass } from '@venture-vibe/icons'
import { clsx } from '@venture-vibe/utils'

export default function BadgePage() {
  return (
    <div className={clsx('min-h-screen w-screen p-8', 'bg-bg')}>
      <div className='mx-auto max-w-6xl space-y-12'>
        <section>
          <div className='flex flex-wrap gap-4'>
            <Badge variant='primary' text='Primary' />
            <Badge variant='primary-accent' text='Primary Accent' />
            <Badge variant='primary-border' text='Primary Border' />
            <Badge variant='secondary' text='Secondary' />
            <Badge variant='secondary-accent' text='Secondary Accent' />
            <Badge variant='secondary-border' text='Secondary Border' />
            <Badge variant='tertiary' text='Tertiary' />
            <Badge variant='tertiary-accent' text='Tertiary Accent' />
            <Badge variant='tertiary-border' text='Tertiary Border' />
            <Badge variant='quaternary' text='Quaternary' />
            <Badge variant='quaternary-accent' text='Quaternary Accent' />
            <Badge variant='quaternary-border' text='Quaternary Border' />
            <Badge variant='danger' text='Danger' />
            <Badge variant='blue' text='Blue' />
          </div>
        </section>
        <section>
          <div className='flex items-center gap-4'>
            <Badge size='xs' variant='secondary-accent' text='Extra Small' />
            <Badge size='sm' variant='secondary-accent' text='Small' />
            <Badge size='md' variant='secondary-accent' text='Medium' />
            <Badge size='lg' variant='secondary-accent' text='Large' />
          </div>
        </section>
        <section>
          <div className='flex flex-wrap gap-4'>
            <Badge roundness='xs' variant='primary-accent' text='XS Round' />
            <Badge roundness='sm' variant='primary-accent' text='SM Round' />
            <Badge roundness='md' variant='primary-accent' text='MD Round' />
            <Badge roundness='lg' variant='primary-accent' text='LG Round' />
            <Badge roundness='xl' variant='primary-accent' text='XL Round' />
            <Badge roundness='xxl' variant='primary-accent' text='XXL Round' />
            <Badge roundness='max' variant='primary-accent' text='Max Round' />
          </div>
        </section>
        <section>
          <div className='space-y-4'>
            <div>
              <div className='flex flex-wrap gap-4'>
                <Badge leftIcon={<ArrowRight />} text='Featured' variant='primary' />
                <Badge leftIcon={<ArrowRight />} text='Verified' variant='secondary' />
                <Badge leftIcon={<ArrowRight />} text='Premium' variant='tertiary' />
                <Badge leftIcon={<ArrowRight />} text='Completed' variant='danger' />
              </div>
            </div>
            <div>
              <div className='flex flex-wrap gap-4'>
                <Badge rightIcon={<ArrowRight />} text='Next' variant='primary-accent' />
                <Badge rightIcon={<ArrowRight />} text='Continue' variant='secondary-accent' />
                <Badge rightIcon={<ArrowRight />} text='Forward' variant='tertiary-accent' />
                <Badge rightIcon={<ArrowRight />} text='Proceed' variant='blue' />
              </div>
            </div>
            <div>
              <div className='flex flex-wrap gap-4'>
                <Badge leftIcon={<MagnifyingGlass />} rightIcon={<ArrowRight />} text='Success' variant='primary' />
                <Badge leftIcon={<ArrowRight />} rightIcon={<ArrowRight />} text='Premium' variant='secondary' />
                <Badge leftIcon={<MagnifyingGlass />} rightIcon={<ArrowRight />} text='Complete' variant='tertiary' />
              </div>
            </div>
          </div>
        </section>
        <section>
          <div className='flex flex-wrap gap-4'>
            <Badge variant='primary-border' text='Primary Border' />
            <Badge variant='secondary-border' text='Secondary Border' />
            <Badge variant='tertiary-border' text='Tertiary Border' />
            <Badge variant='quaternary-border' text='Quaternary Border' />
          </div>
        </section>
        <section>
          <div className='flex flex-wrap gap-4'>
            <Badge variant='primary-accent' text='Primary Accent' />
            <Badge variant='secondary-accent' text='Secondary Accent' />
            <Badge variant='tertiary-accent' text='Tertiary Accent' />
            <Badge variant='quaternary-accent' text='Quaternary Accent' />
          </div>
        </section>
      </div>
    </div>
  )
}
