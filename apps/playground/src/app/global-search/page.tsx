'use client'
import { GlobalSearchProvider, GlobalSearchTrigger, type SearchResult } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'

const AllLinks: SearchResult[] = [
  { href: '/cards/card', title: 'Component - Card' },
  { href: '/cards/category-card', title: 'Component - Category Card' },
  { href: '/cards/pricing-card', title: 'Component - Pricing Card' },
  { href: '/cards/blog-card', title: 'Component - Blog Card' },
  { href: '/cards/testimonial-card', title: 'Component - Testimonial Card' },
  { href: '/cards/features-card', title: 'Component - Features Card' },
  { href: '/typography/title', title: 'Component - Title' },
  { href: '/typography/text', title: 'Component - Text' },
  { href: '/button', title: 'Component - Button' },
  { href: '/input', title: 'Component - Input & Textarea' },
  { href: '/global-search', title: 'Component - Global Search' },
  { href: '/progress', title: 'Component - Progress' },
  { href: '/badge', title: 'Component - Badge' },
  { href: '/tooltip', title: 'Component - Tooltip' },
  { href: '/avatar', title: 'Component - Avatar' },
  { href: '/faq', title: 'Component - FAQ' },
  { href: '/statistic-widget', title: 'Component - Statistic Widget' },
]

export default function GlobalSearchPage() {
  return (
    <div className={clsx('flex min-h-screen w-screen flex-col items-center justify-center', 'space-y-8 p-4', 'bg-bg')}>
      <GlobalSearchProvider links={AllLinks}>
        <GlobalSearchTrigger className='max-w-232 h-30' placeholder='Search Templates or Components' />
      </GlobalSearchProvider>
    </div>
  )
}
