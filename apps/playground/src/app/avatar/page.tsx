import { Avatar, AvatarGroup } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'

export default function AvatarPage() {
  const sampleImage = 'https://avatars.githubusercontent.com/u/67341887?v=4'
  const sampleImages = [
    'https://avatars.githubusercontent.com/u/67341887?v=4',
    'https://avatars.githubusercontent.com/u/1?v=4',
    'https://avatars.githubusercontent.com/u/2?v=4',
    'https://avatars.githubusercontent.com/u/3?v=4',
    'https://avatars.githubusercontent.com/u/4?v=4',
  ]

  return (
    <div className={clsx('min-h-screen w-screen p-8', 'bg-bg')}>
      <div className='mx-auto max-w-6xl space-y-12'>
        <section>
          <div className='flex items-center gap-4'>
            <Avatar size='sm' fallback='SM' image={sampleImage} alt='Small' />
            <Avatar size='md' fallback='MD' image={sampleImage} alt='Medium' />
            <Avatar size='lg' fallback='LG' image={sampleImage} alt='Large' />
            <Avatar size='xl' fallback='XL' image={sampleImage} alt='Extra Large' />
            <Avatar size='2xl' fallback='2XL' image={sampleImage} alt='2X Large' />
          </div>
        </section>
        <section>
          <div className='grid grid-cols-6 gap-4'>
            <Avatar color='default' fallback='DE' />
            <Avatar color='spring' fallback='SP' />
            <Avatar color='spring-light' fallback='SL' />
            <Avatar color='orange' fallback='PU' />
            <Avatar color='orange-light' fallback='PL' />
            <Avatar color='orange' fallback='OR' />
            <Avatar color='orange-light' fallback='OL' />
            <Avatar color='orange-bright' fallback='OB' />
            <Avatar color='blue' fallback='BL' />
            <Avatar color='red' fallback='RD' />
            <Avatar color='gray' fallback='GR' />
            <Avatar color='gray-dark' fallback='GD' />
            <Avatar color='gray-spanish' fallback='GS' />
          </div>
        </section>
        <section>
          <div className='flex items-center gap-4'>
            <Avatar roundness='xs' fallback='XS' />
            <Avatar roundness='sm' fallback='SM' />
            <Avatar roundness='md' fallback='MD' />
            <Avatar roundness='lg' fallback='LG' />
            <Avatar roundness='xl' fallback='XL' />
            <Avatar roundness='2xl' fallback='2XL' />
            <Avatar roundness='max' fallback='MAX' />
            <Avatar roundness='full' fallback='FULL' />
          </div>
        </section>
        <section>
          <div className='flex items-center gap-4'>
            <Avatar size='md' color='spring' fallback='AA' />
            <Avatar size='md' color='orange' fallback='BB' />
            <Avatar size='md' color='orange' fallback='CC' />
            <Avatar size='md' color='blue' fallback='DD' />
            <Avatar size='md' color='red' fallback='EE' />
            <Avatar size='md' color='gray-dark' fallback='FF' />
          </div>
        </section>
        <section>
          <div className='space-y-8'>
            <div>
              <div className='space-y-4'>
                <div>
                  <AvatarGroup
                    size='sm'
                    data={[
                      { image: sampleImages[0], alt: 'User 1', fallback: 'U1' },
                      { image: sampleImages[1], alt: 'User 2', fallback: 'U2' },
                      { image: sampleImages[2], alt: 'User 3', fallback: 'U3' },
                      { fallback: 'U4', color: 'spring' },
                      { fallback: 'U5', color: 'orange' },
                    ]}
                  />
                </div>
                <div>
                  <p className={clsx('mb-2', 'text-sm', 'text-gray-600')}>Medium</p>
                  <AvatarGroup
                    size='md'
                    data={[
                      { image: sampleImages[0], alt: 'User 1', fallback: 'U1' },
                      { image: sampleImages[1], alt: 'User 2', fallback: 'U2' },
                      { image: sampleImages[2], alt: 'User 3', fallback: 'U3' },
                      { fallback: 'U4', color: 'orange' },
                      { fallback: 'U5', color: 'blue' },
                    ]}
                  />
                </div>
                <div>
                  <p className={clsx('mb-2', 'text-sm', 'text-gray-600')}>Large</p>
                  <AvatarGroup
                    size='lg'
                    data={[
                      { image: sampleImages[0], alt: 'User 1', fallback: 'U1' },
                      { image: sampleImages[1], alt: 'User 2', fallback: 'U2' },
                      { fallback: 'U3', color: 'red' },
                      { fallback: 'U4', color: 'gray-dark' },
                    ]}
                  />
                </div>
              </div>
            </div>
            <div>
              <AvatarGroup
                size='md'
                data={[
                  { fallback: 'AB', color: 'spring' },
                  { fallback: 'CD', color: 'orange' },
                  { fallback: 'EF', color: 'orange' },
                  { fallback: 'GH', color: 'blue' },
                  { fallback: 'IJ', color: 'red' },
                  { fallback: 'KL', color: 'gray-dark' },
                ]}
              />
            </div>
            <div>
              <AvatarGroup
                size='md'
                color='spring'
                data={[
                  { image: sampleImages[0], alt: 'User 1', fallback: 'U1' },
                  { image: sampleImages[1], alt: 'User 2', fallback: 'U2' },
                  { fallback: 'U3' }, // Will use parent color
                  { fallback: 'U4' }, // Will use parent color
                  { fallback: '+5' }, // Often used to show count
                ]}
              />
            </div>
            <div>
              <div className='space-y-4'>
                <div>
                  <AvatarGroup
                    size='md'
                    roundness='xs'
                    data={[
                      { image: sampleImages[0], alt: 'User 1', fallback: 'U1' },
                      { fallback: 'U2', color: 'spring' },
                      { fallback: 'U3', color: 'orange' },
                      { fallback: 'U4', color: 'orange' },
                    ]}
                  />
                </div>
                <div>
                  <AvatarGroup
                    size='md'
                    roundness='lg'
                    data={[
                      { image: sampleImages[1], alt: 'User 1', fallback: 'U1' },
                      { fallback: 'U2', color: 'spring' },
                      { fallback: 'U3', color: 'orange' },
                      { fallback: 'U4', color: 'orange' },
                    ]}
                  />
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}
