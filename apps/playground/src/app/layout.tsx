'use client'

import { Breadcrumb } from '@venture-vibe/components/shared'
import { usePathname } from 'next/navigation'
import { componentSections } from './components-router/data'
import './globals.css'

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()

  const breadcrumbItems = [{ label: 'Home', url: '/' }]

  if (pathname) {
    const pathParts = pathname.split('/').filter(Boolean)

    if (pathParts.length > 0) {
      let currentPath = ''
      for (const part of pathParts) {
        currentPath += `/${part}`
        const component = componentSections.flatMap(section => section.components).find(c => c.href === currentPath)

        if (component) {
          breadcrumbItems.push({ label: component.name, url: component.href })
        }
      }
    }
  }

  return (
    <html lang='en'>
      <body className='antialiased'>
        <Breadcrumb items={breadcrumbItems} />
        {children}
      </body>
    </html>
  )
}
