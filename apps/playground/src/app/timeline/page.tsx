import { type Event, Timeline } from '@venture-vibe/components/shared'

const items: Event[] = [
  {
    date: '01.07.2025',
    title: '<PERSON><PERSON> Kamp<PERSON> Başlangıcı',
    description:
      'Yaz kampımız başlıyor! Kat<PERSON>lımcılar yeni projeler üzerinde çalışacak, mentorluk alacak ve ilham verici konuşmacıları dinleyecekler.',
    slug: 'yaz-kampi-baslangici',
    location: 'Istanbul, TR',
    tags: ['#Kamp', '#Mentorluk', '#Yaz'],
  },
  {
    date: '15.08.2025',
    title: 'Hackathon Günü',
    description:
      'Takımlar bir araya gelerek 24 saat boyunca yenilikçi çözümler geliştirecek. En iyi projeler ödüllendirilecek.',
    slug: 'hackathon-gunu',
    location: 'Istanbul, TR',
    tags: ['#Hackathon', '#Yar<PERSON><PERSON><PERSON>', '#Kodlama'],
  },
  {
    date: '30.09.2025',
    title: 'Demo Sunumları',
    description:
      'Katılımcılar projelerini jüriye ve topluluğa sunacak. Geri bildirimler ve networking fırsatları olacak.',
    slug: 'demo-sunumlari',
    location: 'Istanbul, TR',
    tags: ['#Demo', '#Sunum', '#Networking'],
  },
  {
    date: '10.11.2025',
    title: 'Kapanış ve Sertifika Töreni',
    description: 'Başarılı katılımcılara sertifikaları verilecek ve gelecek yılın planları paylaşılacak.',
    slug: 'sertifika-toreni',
    location: 'Istanbul, TR',
    tags: ['#Sertifika', '#Kapanış', '#Ödül'],
  },
  {
    date: '05.12.2025',
    title: 'Mentorlarla Buluşma',
    description: 'Katılımcılar mentorlarla birebir görüşmeler yaparak kariyer tavsiyeleri alacak.',
    slug: 'mentorlarla-bulusma',
    location: 'Istanbul, TR',
    tags: ['#Mentor', '#Kariyer', '#Buluşma'],
  },
  {
    date: '20.12.2025',
    title: 'Topluluk Etkinliği',
    description: 'Tüm katılımcılar ve mezunlar bir araya gelerek deneyimlerini paylaşacak ve yeni bağlantılar kuracak.',
    slug: 'topluluk-etkinligi',
    location: 'Istanbul, TR',
    tags: ['#Topluluk', '#Etkinlik', '#Mezun'],
  },
  {
    date: '15.01.2026',
    title: 'Kış Atölyesi',
    description: 'Katılımcılar yeni teknolojiler üzerine atölye çalışmalarına katılacak.',
    slug: 'kis-atolyesi',
    location: 'Istanbul, TR',
    tags: ['#Kış', '#Atölye', '#Teknoloji'],
  },
  {
    date: '28.02.2026',
    title: 'Proje Değerlendirme',
    description: 'Projeler uzmanlar tarafından değerlendirilecek ve geri bildirimler sunulacak.',
    slug: 'proje-degerlendirme',
    location: 'Istanbul, TR',
    tags: ['#Proje', '#Değerlendirme', '#Uzman'],
  },
  {
    date: '10.01.2026',
    title: 'Networking Kahvaltısı',
    description: 'Katılımcılar ve sektör profesyonelleri bir araya gelerek iş fırsatlarını konuşacak.',
    slug: 'networking-kahvaltisi',
    location: 'Istanbul, TR',
    tags: ['#Networking', '#Kahvaltı', '#Sektör'],
  },
]

export default function TimelinePage() {
  return (
    <div className='bg-gray-50 min-h-screen'>
      <div className='container mx-auto py-16'>
        <Timeline items={items} />
      </div>
    </div>
  )
}
