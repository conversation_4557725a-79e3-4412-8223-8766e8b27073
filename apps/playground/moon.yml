language: "typescript"
type: "library"
platform: "bun"

tasks:
  dev:
    local: true
    command: "next dev --turbopack -H 0.0.0.0 -p 3001"
    deps: ["icons:dev", "components:dev", "utils:dev"]
    env:
      NODE_ENV: development
    inputs:
      - "src/**/*"
    outputs:
      - ".next/**"
  build:
    script: "next build"
    toolchain: 'bun'
    deps: ["icons:build", "components:build","utils:build"]
    inputs:
      - "src/**/*"
    outputs:
      - ".next/**"
  start:
    local: true
    deps: ["client:build"]
    command: "next start -H 0.0.0.0 -p 3001"
    inputs:
      - ".next/**"
  docker:
    command: "bun run next start -H 0.0.0.0 -p 3001"
    local: true
    inputs:
      - ".next/**"