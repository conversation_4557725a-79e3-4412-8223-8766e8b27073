import { type NextRequest, NextResponse } from 'next/server'

export function middleware(request: NextRequest) {
  const response = NextResponse.next()

  // Mevcut dil cookie'sini kontrol et
  const currentLang = request.cookies.get('ozaco-lang')?.value

  // Eğer dil cookie'si yoksa, browser'ın accept-language header'ından al
  if (!currentLang) {
    const acceptLanguage = request.headers.get('accept-language')
    const defaultLocale = acceptLanguage?.split(',')[0]?.split('-')[0]?.toLowerCase()
    const lang = defaultLocale || 'en'

    // Cookie'yi set et
    response.cookies.set('ozaco-lang', lang, {
      httpOnly: false,
      // biome-ignore lint/style/noProcessEnv: Redundant
      // biome-ignore lint/nursery/noProcessGlobal: Redundant
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      // biome-ignore lint/nursery/noMagicNumbers: Redundant
      maxAge: 60 * 60 * 24 * 365, // 1 yıl
    })
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
