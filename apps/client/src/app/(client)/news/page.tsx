import {
  <PERSON><PERSON>ar,
  <PERSON><PERSON>,
  Card,
  GlobalSearchProvider,
  GlobalSearchTrigger,
  GridFlexContainer,
  Text,
} from '@venture-vibe/components/shared'
import Link from 'next/link'
import type { Metadata } from 'next/types'
import { cardNewsData } from '../test-data'

// biome-ignore lint/style/useComponentExportOnlyModules: Metadata
export const metadata: Metadata = {
  title: '<PERSON><PERSON><PERSON>',
}

export interface NewsPageSearchParams extends Record<string, string | string[] | undefined> {
  tag?: string | string[]
}

export interface NewsPageProps {
  searchParams?: Promise<NewsPageSearchParams>
}

export default async function NewsPage(props: NewsPageProps) {
  const searchParams = await props.searchParams
  const selectedTag = searchParams?.tag || 'all'

  if (Array.isArray(selectedTag)) {
    return null
  }

  const allTags = (() => {
    const tags = cardNewsData.flatMap(news => news.tags)
    return Array.from(new Set(tags))
  })()

  const filteredNews = (() => {
    if (selectedTag === 'all') {
      return cardNewsData
    }
    return cardNewsData.filter(news => news.tags.includes(selectedTag))
  })()

  const searchLinks = (() => {
    return cardNewsData.map(news => ({
      href: `/news/${news.slug}`,
      title: news.title,
    }))
  })()

  return (
    <section className='w-full bg-bg pb-50'>
      <div className='flex flex-col px-10 mx-auto max-w-[82.5rem] desktop:gap-28 tablet:gap-20 max-tablet:gap-15'>
        <div className='w-full'>
          <GridFlexContainer layout='flex' direction='column' className='w-full md:flex-row gap-10 mt-60'>
            {/* Right Side: Tag Filter */}
            <Appear direction='right' className='md:w-1/4 w-full'>
              <div className='sticky top-60 bg-white p-5 rounded-2xl'>
                <Text as='p' size='md' className='mb-10' thickness='bold'>
                  Kategoriler
                </Text>
                <GridFlexContainer layout='flex' direction='row' wrap='wrap' gap='sm' className=''>
                  <Link href='/news' scroll={false}>
                    <Button
                      key='all'
                      size='sm'
                      color={selectedTag === 'all' ? 'primary' : 'outlineBlack'}
                      className='w-auto border-platinum-stroke'>
                      <Text as='p' size='md'>
                        Tümü
                      </Text>
                    </Button>
                  </Link>
                  {allTags.map(tag => (
                    <Link key={tag} href={`/news?tag=${tag}`} scroll={false}>
                      <Button
                        size='sm'
                        color={selectedTag === tag ? 'primary' : 'outlineBlack'}
                        className='w-auto border-platinum-stroke'>
                        <Text as='p' size='md'>
                          #{tag}
                        </Text>
                      </Button>
                    </Link>
                  ))}
                </GridFlexContainer>
              </div>
            </Appear>
            {/* Left Side: News Grid */}
            <Appear className='md:w-3/4 w-full' late>
              {/* GlobalSearch'te kategoriler ve sluglar gösterilecek */}
              <GlobalSearchProvider links={searchLinks}>
                <GlobalSearchTrigger
                  className='w-full h-32 rounded-xl mb-10'
                  placeholder='Haber başlığı veya kategori ara'
                />
              </GlobalSearchProvider>
              <Appear className='h-full w-full' late>
                <GridFlexContainer layout='grid' columns={3} gap='lg'>
                  {filteredNews.map(news => (
                    <Card
                      key={news.id}
                      tags={news.tags.map(v => `#${v}`)} // Etiketlerin başına # ekliyoruz
                      type='primary'
                      link={`/news/${news.slug}`}
                      title={news.title}
                      description={news.description}
                      image={news.image}
                      date={news.date}
                      writer={news.writer}
                    />
                  ))}
                </GridFlexContainer>
              </Appear>
            </Appear>
          </GridFlexContainer>
        </div>
      </div>
    </section>
  )
}
