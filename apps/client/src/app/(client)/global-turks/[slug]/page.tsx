/** biome-ignore-all lint/nursery/useImageSize: <ferhatin isleri> */
/** biome-ignore-all lint/performance/noImgElement: <yine ferhatin isleri> */
import { Appear, Breadcrumb, Link, Title, Video } from '@venture-vibe/components/shared'
import { notFound } from 'next/navigation'
import { useId } from 'react'
import { globalTurksData } from '../../test-data'

const getGlobalTurkBySlug = (slug: string) => {
  return globalTurksData.find(tool => tool.slug === slug)
}

const GlobalTurksDetail = async (props: { params: Promise<{ slug: string }> }) => {
  const baseId = useId()
  const { slug } = await props.params
  const globalTurk = getGlobalTurkBySlug(slug)

  if (!globalTurk) {
    return notFound()
  }

  return (
    <section className='w-full bg-bg py-60'>
      <div className='flex flex-col w-full px-10 mx-auto desktop:max-w-[82.5rem] desktop:gap-28 tablet:gap-20 max-tablet:gap-15'>
        <Appear className='w-full flex md:flex-row flex-col-reverse gap-8'>
          {/* Main Content */}
          <div className='md:w-4/5 w-full'>
            <Breadcrumb
              items={[
                { label: 'Global Türkler', url: '/global-turks' },
                { label: globalTurk.title, url: `/global-turks/${globalTurk.slug}` },
              ]}
            />

            <Title as='h1' size='3xl' thickness='bold' className='mt-4'>
              {globalTurk.title}
            </Title>

            <div className='my-8'>
              {globalTurk.videoLink && (
                <Video type='youtube' src={globalTurk.videoLink} className='w-full rounded-2xl' />
              )}
            </div>

            <div className='prose lg:prose-xl max-w-none'>
              {globalTurk.subheadings.map(subheading => {
                const sectionId = `${baseId}-${subheading.id}`
                return (
                  <section key={sectionId} id={sectionId} className='mt-8 scroll-mt-28'>
                    <h2 className='text-2xl font-bold mb-4'>{subheading.title}</h2>
                    <p>{subheading.text}</p>
                  </section>
                )
              })}
            </div>
          </div>

          {/* Table of Contents */}
          <aside className='md:w-1/5 w-full md:sticky md:top-28 md:h-screen h-auto '>
            <div className='bg-bg p-10 rounded-2xl'>
              <h3 className='text-lg font-bold mb-4'>İçindekiler</h3>
              <ul className='space-y-2 list-disc md:block flex flex-wrap'>
                {globalTurk.subheadings.map(subheading => {
                  const sectionId = `${baseId}-${subheading.id}`
                  return (
                    <li className='mx-10' key={subheading.id}>
                      <Link href={`#${sectionId}`} className='text-blue-600 hover:underline'>
                        {subheading.title}
                      </Link>
                    </li>
                  )
                })}
              </ul>
            </div>
          </aside>
        </Appear>
      </div>
    </section>
  )
}

export default GlobalTurksDetail
