import { Appear, GlobalSearchProvider, GlobalSearchTrigger, PersonCard } from '@venture-vibe/components/shared'
import type { Metadata } from 'next/types'
import { useMemo } from 'react'
import { cardGlobalTurks } from '../test-data/index'

// biome-ignore lint/style/useComponentExportOnlyModules: Metadata
export const metadata: Metadata = {
  title: 'Global Türkler',
}

export default function NewsPage() {
  const searchLinks = useMemo(() => {
    const slugLinks = cardGlobalTurks.map(globalTurk => ({
      href: `/global-turks/${globalTurk.slug}`,
      title: globalTurk.name,
    }))
    return [...slugLinks]
  }, [])

  return (
    <section className='w-full bg-bg pb-50'>
      <div className='flex flex-col px-10 mx-auto max-w-[82.5rem] desktop:gap-28 tablet:gap-20 max-tablet:gap-15'>
        <div className='w-full'>
          <div className='w-full flex flex-col md:flex-row gap-6 mt-60'>
            <Appear className='w-full'>
              {/* GlobalSearch'te kategoriler ve sluglar gösterilecek */}
              <GlobalSearchProvider links={searchLinks}>
                <GlobalSearchTrigger className='w-full h-32 rounded-xl mb-10' placeholder='Global Türkleri ara' />
              </GlobalSearchProvider>
              <Appear className='h-full w-full' late>
                <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
                  {cardGlobalTurks.map(globalTurk => (
                    <PersonCard
                      key={globalTurk.slug}
                      link={`/global-turks/${globalTurk.slug}`}
                      title={globalTurk.title}
                      name={globalTurk.name}
                      description={globalTurk.description}
                      image={globalTurk.image}
                    />
                  ))}
                </div>
              </Appear>
            </Appear>
          </div>
        </div>
      </div>
    </section>
  )
}
