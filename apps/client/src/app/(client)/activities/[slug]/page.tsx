/** biome-ignore-all lint/nursery/useImageSize: <zipzipzip> */
/** biome-ignore-all lint/performance/noImgElement: <nazicuk31> */
import { Appear, Badge, Breadcrumb, Text, Title } from '@venture-vibe/components/shared'
import { notFound } from 'next/navigation'
import type { EventDetail } from '../../../../types/events'
import { eventDetailsData } from '../../test-data'

const getEventBySlug = (slug: string): EventDetail | undefined => {
  return eventDetailsData.find(event => event.slug === slug)
}

export default async function ActivityDetail({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params
  const event = getEventBySlug(slug)

  if (!event) {
    notFound()
  }
  return (
    <section className='w-full bg-bg py-60'>
      <div className='flex flex-col w-full px-10 mx-auto desktop:max-w-[82.5rem] desktop:gap-28 tablet:gap-20 max-tablet:gap-15'>
        <Appear className='w-full flex flex-col gap-8'>
          <Breadcrumb
            items={[
              { label: 'Etkinlikler', url: '/activities' },
              { label: event.title, url: `/activities/${event.slug}` },
            ]}
          />

          <Title as='h1' size='3xl' thickness='bold' className='mt-4'>
            {event.title}
          </Title>

          <div className='flex flex-wrap gap-4 my-4'>
            {event.tags.map(tag => (
              <Badge
                key={tag}
                variant='quaternary-border'
                text={tag}
                className='border-none px-0 text-2xs sm:text-xs'
              />
            ))}
          </div>

          <div className='flex gap-8 text-gray-600'>
            <div className='flex flex-row gap-3'>
              <Text thickness='bold'>Tarih:</Text>
              <Text>{event.date}</Text>
            </div>

            <div className='flex flex-row gap-3'>
              <Text thickness='bold'>Konum:</Text>
              <Text>{event.location}</Text>
            </div>
          </div>

          <img
            src={event.image}
            alt={event.title}
            className='w-full h-auto max-h-[500px] object-cover rounded-lg my-8'
          />

          <p>{event.description}</p>
        </Appear>
      </div>
    </section>
  )
}
