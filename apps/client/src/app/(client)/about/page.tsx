import {
  Appear,
  BackgroundS<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Link,
  Noise,
  Text,
  Title,
} from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'
import type { Metadata } from 'next/types'

// biome-ignore lint/style/useComponentExportOnlyModules: Metadata
export const metadata: Metadata = {
  title: 'Hakkımızda',
}

export default function About() {
  return (
    <>
      <Hero as='section' className='w-full h-max'>
        <BackgroundSection
          as='div'
          padding={null}
          layers={[
            { type: 'color', color: '#C5F910' },
            {
              type: 'node',
              node: (
                <div className='flex justify-center items-center absolute inset-0 bg-cover bg-center h-full'>
                  {/** biome-ignore lint/performance/noImgElement: Reduntant */}
                  <img
                    src='/grid.svg'
                    alt='a'
                    width={520}
                    height={520}
                    className='shrink-0 desktop:min-w-[1340px] desktop:min-h-[900px] tablet:min-w-[1024px] tablet:min-h-[768px] max-tablet:min-w-[768px] max-tablet:min-h-[660px]'
                  />
                </div>
              ),
            },
            {
              type: 'node',
              node: (
                <div className='w-full h-full'>
                  <Noise className='w-full h-full' />
                </div>
              ),
            },
          ]}
          className='pb-[80px] desktop:h-[528px] desktop:pt-[192px] desktop:pb-[124px] tablet:h-[430px] tablet:pt-[160px] max-tablet:h-[448px] max-tablet:pt-[140px]'>
          <Appear className='flex flex-col gap-8 w-full justify-center items-center px-10'>
            <Badge text='Hakkımızda' className='w-fit tablet:p-10 max-tablet:p-8' />
            <div className='flex flex-col gap-12 justify-center items-center'>
              <Title
                as='h2'
                size={null}
                className='font-display desktop:text-[56px] tablet:text-[46px] max-tablet:text-[40px]'>
                Venture Vibe
              </Title>
              <Text size={null} className='text-center tablet:max-w-[680px] tablet:text-[18px] max-tablet:text-[16px]'>
                Girişimcilik ekosistemine değer katan, yenilikçi fikirleri görünür kılan ve global fırsatları yerel
                girişimcilerle buluşturan bir dijital içerik platformuyuz.
              </Text>
            </div>
          </Appear>
        </BackgroundSection>
      </Hero>
      <section className='flex flex-row justify-center items-center px-10 w-full desktop:py-[96px] tablet:py-[80px] max-tablet:py-[60px]'>
        <div className='flex flex-col desktop:flex-row justify-between items-start w-full desktop:max-w-[1280px] tablet:max-w-[790px] gap-10 desktop:gap-20'>
          <Appear direction='right' className='flex flex-col gap-12 w-full desktop:w-1/2'>
            <div className='flex flex-col gap-8'>
              <Title as='h4' size={null} className='desktop:text-[40px] tablet:text-[34px] max-tablet:text-[30px]'>
                Misyonumuz & Vizyonumuz
              </Title>
              <Text size={null} className='tablet:text-[18px] max-tablet:text-[16px]'>
                Türkiye’nin girişimcilik vizyonunu global arenada güçlendirmek için çalışıyoruz.
              </Text>
            </div>
            {/** biome-ignore lint/performance/noImgElement: Reduntant */}
            <img
              width={0}
              height={0}
              src='https://images.unsplash.com/photo-1556761175-b413da4baf72?q=80&w=1974&auto=format&fit=crop'
              alt='Ekip çalışması'
              className='w-full h-auto rounded-3xl object-cover'
            />
            <div className='w-full h-full mx-auto'>
              <div className='flex flex-col gap-10 font-display text-dark-gray'>
                <div className='flex flex-col gap-4'>
                  <Title as='h6' size={null} className='text-[20px]'>
                    Misyonumuz
                  </Title>
                  <Text as='p' size='lg' className='text-gray-600'>
                    Girişimciler, yatırımcılar, kurumlar ve yaratıcı profesyoneller arasında sürdürülebilir bağlar
                    kurmak, bilgi paylaşımını teşvik etmek ve Türkiye’nin girişimcilik vizyonunu global arenada
                    güçlendirmektir.
                  </Text>
                </div>
                <div className='flex flex-col gap-4'>
                  <Title as='h6' size={null} className='text-[20px]'>
                    Vizyonumuz
                  </Title>
                  <Text as='p' size='lg' className='text-gray-600'>
                    Yalnızca bir haber ve içerik kaynağı değil, aynı zamanda fikirlerin buluştuğu, iş birliklerinin
                    doğduğu ve ilhamın paylaşıldığı bir topluluk alanı olmak.
                  </Text>
                </div>
              </div>
            </div>
          </Appear>
          <Appear
            direction='left'
            late
            className={clsx(
              'flex flex-col items-start justify-start',
              'w-full desktop:w-1/2',
              'space-y-8',
              'bg-white',
              'border border-platinum-stroke',
              'p-14',
              'rounded-[32px]',
            )}>
            <div className={clsx('flex flex-col space-y-8', 'w-full')}>
              <div>
                <Title as='h4' size={null} className='desktop:text-[30px] tablet:text-[28px] max-tablet:text-[24px]'>
                  Neler Sunuyoruz?
                </Title>
              </div>
              <Text as='p' className='text-gray-600'>
                Startup dünyasındaki en son gelişmelerden yatırım trendlerine; yapay zeka ve ileri teknolojilerden
                yaratıcı endüstrilere kadar geniş bir yelpazede içerikler sunuyoruz.
              </Text>
              <Text as='p' className='text-gray-600'>
                İlham verici röportajlar, derinlemesine analizler, ekosistem haberleri ve etkinlik duyuruları ile
                girişimcilik yolculuğunuzun her adımında yanınızdayız.
              </Text>
              <Text as='p' className='text-gray-600'>
                Venture Vibe, yalnızca bir haber ve içerik kaynağı değil, aynı zamanda fikirlerin buluştuğu, iş
                birliklerinin doğduğu ve ilhamın paylaşıldığı bir topluluk alanıdır.
              </Text>
            </div>
            <Link href='/contact-us' className='w-full'>
              <Button color='secondary' size='lg' className='w-full mt-4 text-md'>
                Bizimle İletişime Geçin
              </Button>
            </Link>
          </Appear>
        </div>
      </section>
    </>
  )
}
