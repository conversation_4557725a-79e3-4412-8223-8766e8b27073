/** biome-ignore-all lint/nursery/useImageSize: <ferhatin isleri> */
/** biome-ignore-all lint/performance/noImgElement: <yine ferhatin isleri> */
import { Appear, Breadcrumb, Carousel, Link, Title } from '@venture-vibe/components/shared'
import { notFound } from 'next/navigation'
import { useId } from 'react'
import { aiToolDetails } from '../../test-data'

const MAX_IMAGE_SIZE = 4

const getToolBySlug = (slug: string) => {
  return aiToolDetails.find(tool => tool.slug === slug)
}

const BlogDetail = async (props: { params: Promise<{ slug: string }> }) => {
  const baseId = useId()
  const { slug } = await props.params
  const tool = getToolBySlug(slug)

  if (!tool) {
    return notFound()
  }

  return (
    <section className='w-full bg-bg py-60'>
      <div className='flex flex-col w-full px-10 mx-auto desktop:max-w-[82.5rem] desktop:gap-28 tablet:gap-20 max-tablet:gap-15'>
        <Appear className='w-full flex md:flex-row flex-col-reverse gap-8'>
          {/* Main Content */}
          <div className='md:w-4/5 w-full'>
            <Breadcrumb
              items={[
                { label: 'AI Araçları', url: '/ai-tools' },
                { label: tool.title, url: `/ai-tools/${tool.slug}` },
              ]}
            />

            <Title as='h1' size='3xl' thickness='bold' className='mt-4'>
              {tool.title}
            </Title>

            <div className='my-8'>
              <Carousel loop autoplay showArrows showDots dotsVariant='preview'>
                {tool.images.map((src, index) => (
                  <img
                    key={String(index)}
                    src={`${src}`}
                    alt={`Haber görseli ${index + 1}`}
                    className='w-full h-full object-cover rounded-lg'
                  />
                ))}
              </Carousel>
            </div>

            <div className='prose lg:prose-xl max-w-none'>
              {tool.subheadings.map(subheading => {
                const sectionId = `${baseId}-${subheading.id}`
                return (
                  <section key={sectionId} id={sectionId} className='mt-8 scroll-mt-28'>
                    <h2 className='text-2xl font-bold mb-4'>{subheading.title}</h2>
                    <p>{subheading.text}</p>
                  </section>
                )
              })}
            </div>
          </div>

          {/* Table of Contents */}
          <aside className='md:w-1/5 w-full md:sticky md:top-28 md:h-screen h-auto '>
            <div className='bg-bg p-10 rounded-2xl'>
              <h3 className='text-lg font-bold mb-4'>İçindekiler</h3>
              <ul className='space-y-2 list-disc md:block flex flex-wrap'>
                {tool.subheadings.map(subheading => {
                  const sectionId = `${baseId}-${subheading.id}`
                  return (
                    <li className='mx-10' key={subheading.id}>
                      <Link href={`#${sectionId}`} className='text-blue-600 hover:underline'>
                        {subheading.title}
                      </Link>
                    </li>
                  )
                })}
              </ul>
            </div>
          </aside>
        </Appear>
      </div>
    </section>
  )
}

export default BlogDetail
