import {
  <PERSON>ppear,
  GlobalSearchProvider,
  GlobalSearchTrigger,
  Link,
  ToolCard,
  type ToolCardProps,
} from '@venture-vibe/components/shared'
import type { Metadata } from 'next/types'
import { aiToolsData } from '../test-data'

const MAX_GROUP_LENGTH = 4

// biome-ignore lint/style/useComponentExportOnlyModules: Metadata
export const metadata: Metadata = {
  title: 'AI Araçları',
}

export default function AiTools() {
  const groups: (ToolCardProps | ToolCardProps[])[] = []

  for (const tool of aiToolsData) {
    if (tool.size === 'lg') {
      groups.push(tool)
      continue
    }

    const last = groups.at(-1)

    if (last === undefined || !Array.isArray(last)) {
      groups.push([tool])
      continue
    }

    if (last.length === MAX_GROUP_LENGTH) {
      groups.push([tool])
    } else {
      last.push(tool)
    }
  }

  const searchLinks = (() => {
    return aiToolsData.map(blog => ({
      href: `/ai-tools/${blog.title}`,
      title: blog.title,
    }))
  })()

  return (
    <section className='w-full bg-bg pb-50'>
      <div className='flex flex-col px-10 mx-auto max-w-[82.5rem] desktop:gap-28 lg:gap-20 max-lg:gap-15'>
        <div className='w-full'>
          <div className='flex flex-col w-full gap-10 mt-60'>
            <GlobalSearchProvider links={searchLinks}>
              <Appear className='w-full h-full'>
                <GlobalSearchTrigger className='w-full h-32 rounded-xl mb-10' placeholder='AI aracı ara' />
              </Appear>
            </GlobalSearchProvider>
            <div className='w-full flex justify-center'>
              <Appear className='w-full max-w-[1280px] grid place-items-center grid-cols-1 sm:grid-cols-2 gap-10' late>
                {groups.map((v, i) => {
                  if (Array.isArray(v)) {
                    return (
                      <div
                        key={Number(i)}
                        className='w-full h-full grid place-items-center grid-cols-1 sm:grid-cols-2 sm:grid-rows-2 sm:col-span-2 lg:col-span-1 gap-10'>
                        {v.map(v2 => (
                          <Appear
                            key={v2.title}
                            options={{ threshold: 0.4 }}
                            className='flex justify-center w-full h-full'>
                            <Link href={`/ai-tools/${v2.title}`} className='w-full'>
                              <ToolCard {...v2} />
                            </Link>
                          </Appear>
                        ))}
                      </div>
                    )
                  }
                  return (
                    <Appear key={v.title} options={{ threshold: 0.4 }} className='flex justify-center w-full h-full'>
                      <Link href={`/ai-tools/${v.title}`} className='w-full'>
                        <ToolCard {...v} />
                      </Link>
                    </Appear>
                  )
                })}
              </Appear>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
