import {
  <PERSON>ppear,
  Badge,
  Button,
  GlobalSearchProvider,
  GlobalSearchTrigger,
  GridFlexContainer,
  ProjectCard,
  Text,
} from '@venture-vibe/components/shared'
import { cardJobsData } from '../test-data'

export interface JobsPageSearchParams extends Record<string, string | string[] | undefined> {
  tag?: string | string[]
  type?: string | string[]
}

export interface JobsPageProps {
  searchParams?: Promise<JobsPageSearchParams>
}

export default async function JobsPage(props: JobsPageProps) {
  const searchParams = await props.searchParams
  const selectedTag = searchParams?.tag || 'all'
  const selectedTypes = Array.isArray(searchParams?.type)
    ? searchParams?.type
    : searchParams?.type
      ? [searchParams.type]
      : []

  if (Array.isArray(selectedTag)) {
    return null
  }
  const buildTypeFilterUrl = (type: string) => {
    const newTypes = selectedTypes.includes(type) ? selectedTypes.filter(t => t !== type) : [...selectedTypes, type]
    const params = new URLSearchParams()
    if (selectedTag !== 'all') {
      params.append('tag', selectedTag)
    }
    for (const t of newTypes) {
      params.append('type', t)
    }

    return `/jobs${params.toString() ? `?${params.toString()}` : ''}`
  }

  const buildTagFilterUrl = (tag: string) => {
    const params = new URLSearchParams()
    if (tag !== 'all') {
      params.append('tag', tag)
    }
    for (const t of selectedTypes) {
      params.append('type', t)
    }

    return `/jobs${params.toString() ? `?${params.toString()}` : ''}`
  }

  const allTags = (() => {
    const tags = cardJobsData.flatMap(job => job.tags)
    return Array.from(new Set(tags))
  })()

  const filteredJobs = (() => {
    let jobs = cardJobsData
    if (selectedTag !== 'all') {
      jobs = jobs.filter(job => job.tags.includes(selectedTag))
    }
    if (selectedTypes.length > 0) {
      jobs = jobs.filter(job => selectedTypes.includes(job.type))
    }
    return jobs
  })()

  const searchLinks = (() => {
    return cardJobsData.map(job => ({
      href: `/jobs/${job.slug}`,
      title: job.title,
      category: job.company,
    }))
  })()

  return (
    <section className='w-full bg-bg pb-50'>
      <div className='flex flex-col px-10 mx-auto max-w-[82.5rem] desktop:gap-28 tablet:gap-20 max-tablet:gap-15'>
        <div className='w-full'>
          <GridFlexContainer layout='flex' direction='column' className='w-full md:flex-row gap-10 mt-60'>
            {/* Right Side: Tag Filter */}
            <Appear direction='right' className='md:w-1/4 w-full gap-10 flex flex-col'>
              <div className='sticky top-60 gap-10 flex flex-col'>
                <div className='bg-white p-5 rounded-2xl'>
                  <Text as='p' size='md' className='mb-10' thickness='bold'>
                    Kategoriler
                  </Text>
                  <GridFlexContainer layout='flex' direction='row' wrap='wrap' gap='sm' className=''>
                    <Button
                      key='all'
                      size='sm'
                      color={selectedTag === 'all' ? 'primary' : 'outlineBlack'}
                      href={buildTagFilterUrl('all')}
                      className='w-auto border-platinum-stroke'>
                      <Text as='p' size='md'>
                        Tümü
                      </Text>
                    </Button>
                    {allTags.map(tag => (
                      <Button
                        key={tag}
                        size='sm'
                        color={selectedTag === tag ? 'primary' : 'outlineBlack'}
                        href={buildTagFilterUrl(tag)}
                        className='w-auto border-platinum-stroke'>
                        <Text as='p' size='md'>
                          {tag}
                        </Text>
                      </Button>
                    ))}
                  </GridFlexContainer>
                </div>
                <div className='bg-white p-5 rounded-2xl'>
                  <Text as='p' size='md' className='mb-4' thickness='bold'>
                    İş Tipi
                  </Text>
                  <div className='flex flex-row gap-4'>
                    <Button
                      size='sm'
                      color={selectedTypes.includes('freelance') ? 'primary' : 'outlineBlack'}
                      href={buildTypeFilterUrl('freelance')}
                      className='w-auto border-platinum-stroke'>
                      Freelance
                    </Button>
                    <Button
                      size='sm'
                      color={selectedTypes.includes('startup') ? 'primary' : 'outlineBlack'}
                      href={buildTypeFilterUrl('startup')}
                      className='w-auto border-platinum-stroke'>
                      Startup
                    </Button>
                    <Button
                      size='sm'
                      color={selectedTypes.includes('project') ? 'primary' : 'outlineBlack'}
                      href={buildTypeFilterUrl('project')}
                      className='w-auto border-platinum-stroke'>
                      Proje
                    </Button>
                  </div>
                </div>
              </div>
            </Appear>
            {/* Left Side: News Grid */}
            <Appear className='md:w-3/4 w-full'>
              <div className='flex flex-row gap-4 justify-between'>
                <GlobalSearchProvider links={searchLinks}>
                  <GlobalSearchTrigger
                    className='w-full h-32 rounded-xl mb-10'
                    placeholder='İş ilanı veya şirket ara'
                  />
                  <Button size='lg' className='whitespace-nowrap h-31 text-sm !rounded-xl' href='/jobs/advert'>
                    İş İlanı Yayınla
                  </Button>
                </GlobalSearchProvider>
              </div>
              <Appear className='h-full w-full' late>
                <GridFlexContainer layout='grid' columns={3} gap='lg'>
                  {filteredJobs.map(job => (
                    <ProjectCard
                      key={job.id}
                      link={`/jobs/${job.slug}`}
                      tags={job.tags}
                      color='light-spring'
                      title={job.title}
                      description={job.description}
                      buttonText='İlanı Görüntüle'
                      badge={
                        <div className='flex flex-row gap-4'>
                          <Badge size='xs' variant='secondary-accent' text={job.company} />
                          <Badge size='xs' variant='quaternary-border' text={job.location} />
                        </div>
                      }
                    />
                  ))}
                </GridFlexContainer>
              </Appear>
            </Appear>
          </GridFlexContainer>
        </div>
      </div>
    </section>
  )
}
