/** biome-ignore-all lint/nursery/useImageSize: <ferhatin isleri> */
/** biome-ignore-all lint/performance/noImgElement: <yine ferhatin isleri> */
import { <PERSON>ppear, Badge, Breadcrumb, Button, GridFlexContainer, Markdown, Title } from '@venture-vibe/components/shared'
import { notFound } from 'next/navigation'
import { cardJobsData } from '../../test-data'

const getJobBySlug = (slug: string) => {
  return cardJobsData.find(job => job.slug === slug)
}

const JobDetail = async (props: { params: Promise<{ slug: string }> }) => {
  const { slug } = await props.params
  const job = getJobBySlug(slug)

  if (!job) {
    return notFound()
  }

  return (
    <section className='w-full bg-bg pb-50'>
      <div className='flex flex-col px-10 mx-auto max-w-[82.5rem] desktop:gap-28 tablet:gap-20 max-tablet:gap-15'>
        <Appear className='w-full'>
          <GridFlexContainer layout='flex' direction='column' className='w-full md:flex-row gap-10 mt-60'>
            {/* Main Content */}
            <Appear className='md:w-4/5 w-full md:col-span-2'>
              <Breadcrumb
                items={[
                  { label: 'İş İlanları', url: '/jobs' },
                  { label: job.title, url: `/jobs/${job.slug}` },
                ]}
              />
              <div className='flex flex-col gap-4 mt-4'>
                <Title as='h1' size='3xl' thickness='bold'>
                  {job.title}
                </Title>
                <p className='text-lg text-muted-foreground'>
                  {job.company} - {job.location}
                </p>
                <Markdown source={job.content} className='prose-lg max-w-none mt-6' />
              </div>
            </Appear>
            {/* Job Overview */}
            <Appear className='md:w-1/5 w-full md:sticky md:top-28 h-fit'>
              <div className='bg-card p-6 rounded-2xl border border-platinum-stroke flex flex-col gap-6'>
                <Title as='h3' size='xl' thickness='semibold'>
                  İşe Genel Bakış
                </Title>
                <div className='flex flex-wrap gap-2'>
                  {job.tags.map(tag => (
                    <Badge key={tag} variant='secondary-accent' text={tag} />
                  ))}
                </div>
                <div className='flex flex-col gap-4'>
                  <div>
                    <p className='font-semibold'>Şirket</p>
                    <p className='text-muted-foreground'>{job.company}</p>
                  </div>
                  <div>
                    <p className='font-semibold'>Konum</p>
                    <p className='text-muted-foreground'>{job.location}</p>
                  </div>
                  <div>
                    <p className='font-semibold'>Pozisyon</p>
                    <p className='text-muted-foreground'>{job.title}</p>
                  </div>
                  <div>
                    <p className='font-semibold'>İletişim</p>
                    <p className='text-muted-foreground'>{job.mail}</p>
                    <p className='text-muted-foreground'>{job.phone}</p>
                    <p className='text-muted-foreground'>{job.address}</p>
                  </div>
                </div>
                <Button href={job.applyUrl} target='_blank' rel='noopener noreferrer' className='w-auto' size='lg'>
                  Hemen Başvur
                </Button>
              </div>
            </Appear>
          </GridFlexContainer>
        </Appear>
      </div>
    </section>
  )
}

export default JobDetail
