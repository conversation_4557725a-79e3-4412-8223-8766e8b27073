import { Button, Input, Text, TextArea, Title } from '@venture-vibe/components/shared'

async function createAdvert(formData: FormData) {
  'use server'

  /**
   * ...
   */

  // biome-ignore lint/suspicious/noConsole: Reduntant
  await console.log(formData)
}

const jobTypes = [
  { label: 'Serbest', value: 'freelance' },
  { label: 'Girişim', value: 'startup' },
  { label: 'Proje', value: 'project' },
]

export default function CreateAdvertPage() {
  return (
    <section className='w-full desktop:py-64 tablet:py-64 max-tablet:py-64 bg-bg'>
      <div className='w-11/12 lg:w-2/4 mx-auto px-10 desktop:max-w-[1280px] tablet:max-w-[790px]'>
        <div className='bg-white w-full p-10 rounded-max border border-platinum-stroke shadow-sm'>
          <Title as='h1' className='text-4xl font-bold mb-4 text-center'>
            <PERSON><PERSON> Oluştur
          </Title>
          <Text className='text-lg text-dark-gray mb-8 text-center'>
            Yeni bir iş ilanı yayınlamak için aşağıdaki bilgileri doldurun.
          </Text>
          <form action={createAdvert} className='space-y-8 w-full'>
            <div className='grid grid-cols-1 w-full md:grid-cols-2 gap-8'>
              <div className='flex w-full flex-col gap-2 md:col-span-2'>
                <Text as='label'>Şirket</Text>
                <Input name='company' placeholder='Venture Vibe' required className='w-full' />
              </div>
              <div className='flex flex-col gap-2 md:col-span-2'>
                <Text as='label'>Konum</Text>
                <Input name='location' placeholder='İstanbul, TR' required className='w-full' />
              </div>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-8 md:col-span-2'>
                <div className='flex flex-col gap-2'>
                  <Text as='label'>İş Unvanı</Text>
                  <Input name='title' placeholder='Backend Developer' required className='w-full' />
                </div>
                <div className='flex flex-col gap-2'>
                  <Text as='label'>Etiketler</Text>
                  <Input name='tags' placeholder='#Istanbul, #Backend, #React' required className='w-full' />
                </div>
              </div>
              <div className='flex flex-col gap-2 md:col-span-2'>
                <Text as='label'>Kısa Açıklama</Text>
                <TextArea
                  name='description'
                  placeholder='İşin kısa bir özeti.'
                  required
                  className='h-24 rounded-xl w-full'
                />
              </div>
              <div className='flex flex-col gap-2 md:col-span-2'>
                <Text as='label'>İş İçeriği (Markdown)</Text>
                <TextArea
                  name='content'
                  placeholder='Markdown formatında detaylı iş açıklaması.'
                  required
                  className='h-64 rounded-xl'
                />
              </div>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-8 md:col-span-2'>
                <div className='flex flex-col gap-2'>
                  <Text as='label'>Başvuru URL'si</Text>
                  <Input
                    name='applyUrl'
                    type='url'
                    placeholder='https://example.com/basvur'
                    required
                    className='w-full'
                  />
                </div>
                <div className='flex flex-col gap-2'>
                  <Text as='label'>Adres</Text>
                  <Input name='address' placeholder='123 Ana Cad, İstanbul, Türkiye' className='w-full' />
                </div>
              </div>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-8 md:col-span-2'>
                <div className='flex flex-col gap-2'>
                  <Text as='label'>İletişim Telefonu</Text>
                  <Input name='phone' type='tel' placeholder='(*************' className='w-full' />
                </div>
                <div className='flex flex-col gap-2'>
                  <Text as='label'>İletişim E-postası</Text>
                  <Input
                    name='mail'
                    type='email'
                    placeholder='örn: <EMAIL>'
                    required
                    className='w-full'
                  />
                </div>
              </div>
              <div className='flex flex-col gap-2 md:col-span-2'>
                <Text as='label' className='font-medium mb-2'>
                  İş Türü
                </Text>
                <div className='flex flex-row gap-10'>
                  {jobTypes.map((type, i) => {
                    return (
                      <label key={Number(i) + 1} htmlFor={`jobType${i}`} className='cursor-pointer flex gap-4'>
                        <input
                          type='radio'
                          key={Number(i)}
                          id={`jobType${i}`}
                          name='type'
                          value={type.value}
                          defaultChecked
                        />
                        {type.label}
                      </label>
                    )
                  })}
                </div>
              </div>
            </div>
            <div className='flex justify-end'>
              <Button type='submit' color='primary' size='lg' className='w-full md:w-auto'>
                İlanı Yayınla
              </Button>
            </div>
          </form>
        </div>
      </div>
    </section>
  )
}
