/** biome-ignore-all lint/performance/noImgElement: Reduntant */
/** biome-ignore-all lint/a11y/useAltText: <immediate> */
/** biome-ignore-all lint/nursery/useImageSize: <immediate> */
import {
  Appear,
  BackgroundSection,
  Button,
  Card,
  CategoryCard,
  GlobalSearchProvider,
  GlobalSearchTrigger,
  GridFlexContainer,
  Hero,
  Link,
  Noise,
  StatisticWidget,
  TestimonialCard,
  Text,
  Title,
} from '@venture-vibe/components/shared'
import { ApprovalTickIcon, ArrowRight, InterfaceIcon, TwoPeopleIcon } from '@venture-vibe/icons'
import { translations } from '../../api/translations'
import { cardBlogsData, cardNewsData } from './test-data'

const ROW_GRID_SIZE = 4

const links = [
  ...cardBlogsData.map(card => ({
    title: card.title,
    href: `blogs/${card.slug}`,
  })),
  ...cardNewsData.map(card => ({
    title: card.title,
    href: `news/${card.slug}`,
  })),
]

export default async function Home() {
  const t = await translations('common')

  return (
    <div className='flex flex-col items-center font-display h-full bg-bg'>
      <Hero as='section' className='w-full h-max'>
        <BackgroundSection
          as='div'
          padding={null}
          layers={[
            { type: 'color', color: '#C5F910' },
            {
              type: 'node',
              node: (
                <div className='flex justify-center items-center absolute inset-0 bg-cover bg-center h-full'>
                  <img
                    src='/grid.svg'
                    alt='a'
                    width={520}
                    height={520}
                    className='shrink-0 desktop:min-w-[1340px] desktop:min-h-[900px] tablet:min-w-[1024px] tablet:min-h-[768px] max-tablet:min-w-[768px] max-tablet:min-h-[660px]'
                  />
                </div>
              ),
            },
            {
              type: 'node',
              node: (
                <div className='w-full h-full'>
                  <Noise className='w-full h-full' />
                </div>
              ),
            },
          ]}
          className='flex items-center justify-center box-border min-h-screen w-full'>
          <GridFlexContainer layout='flex' direction='row' align='center'>
            <GridFlexContainer
              layout='flex'
              direction='column'
              align='start'
              justify='center'
              className='gap-10 min-tablet:items-center'>
              <GridFlexContainer
                layout='flex'
                direction='column'
                align='center'
                justify='center'
                className='gap-10 px-5 min-tablet:px-30'>
                <GridFlexContainer layout='flex' direction='column' align='center' justify='center' className='gap-5'>
                  <Appear>
                    <div className='flex flex-row'>
                      <img src='/podcaster.png' alt='Background' className='w-full h-auto rounded-xl z-10' />
                      <img
                        width={0}
                        height={0}
                        src='/mouth.png'
                        alt='Background'
                        className='absolute right-0 bottom-64 w-35 h-35 hidden tablet:block rounded-xl bg-cover bg-center bg-no-repeat z-10'
                      />
                      <img
                        width={0}
                        height={0}
                        src='/tape.png'
                        alt='Background'
                        className='absolute -right-70 bottom-70 w-65 h-45 hidden tablet:block rounded-xl bg-cover bg-center bg-no-repeat z-10'
                      />
                    </div>
                  </Appear>
                  <Appear late>
                    <GridFlexContainer
                      layout='flex'
                      direction='column'
                      align='center'
                      justify='center'
                      className='gap-10'>
                      <div className='relative h-0 w-[83.5vmin] desktop:w-[650px] tablet:w-[580px]'>
                        <BackgroundSection
                          padding={null}
                          className='absolute -translate-y-1/1 h-[16.05vmin] desktop:h-[125px] tablet:h-[110px] flex justify-center items-center'
                          layers={[
                            {
                              type: 'node',
                              node: (
                                <Title
                                  as='h1'
                                  className='font-header [-webkit-text-stroke:25px_#000] font-bold w-fit text-center text-bg text-[6.25vmin] leading-[7.8vmin] desktop:text-[48px] desktop:leading-30 tablet:text-[43px] tablet:leading-26'
                                  thickness='bold'>
                                  İlhamla Başla,
                                  <br />
                                  Bağlantıyla Büyü
                                </Title>
                              ),
                              className: 'left-[1.75vmin] desktop:left-[16px] tablet:left-[15px]',
                            },
                            {
                              type: 'node',
                              node: (
                                <Title
                                  as='h1'
                                  className='font-header font-bold w-fit text-center text-bg text-[6.25vmin] leading-[7.8vmin] desktop:text-[48px] desktop:leading-30 tablet:text-[43px] tablet:leading-26'
                                  thickness='bold'>
                                  {t.hello}
                                  <br />
                                  Bağlantıyla Büyü
                                </Title>
                              ),
                              className: 'left-[1.75vmin] desktop:left-[16px] tablet:left-[15px]',
                            },
                          ]}
                        />
                      </div>
                      <Text
                        as='p'
                        size={null}
                        className='max-w-full text-center text-[18px] text-[#6F6F6F] leading-16 tablet:max-w-450 max-tablet:max-w-300 max-tablet:text-left max-tablet:text-[1rem] max-tablet:leading-14'>
                        Girişimcilik, teknoloji ve AI dünyasından en güncel içerikler, global başarı hikayeleri ve
                        güvenilir iş fırsatları… VentureVibe ile fikirlerini hayata geçir, doğru insanlarla tanış,
                        birlikte büyü.
                      </Text>
                    </GridFlexContainer>
                  </Appear>
                </GridFlexContainer>
                <Appear className='w-full' late>
                  <GridFlexContainer layout='flex' direction='column' align='center' justify='center' className='gap-4'>
                    <GlobalSearchProvider links={links}>
                      <GlobalSearchTrigger
                        className='w-full tablet:max-w-232 h-26 tablet:h-30'
                        placeholder='Haber veya Blog Ara'
                      />
                    </GlobalSearchProvider>
                  </GridFlexContainer>
                </Appear>
              </GridFlexContainer>
            </GridFlexContainer>
          </GridFlexContainer>
        </BackgroundSection>
      </Hero>

      <section className='w-full desktop:py-48 tablet:py-40 max-tablet:py-30'>
        <Appear className='w-full'>
          <div className='w-full px-10 mx-auto desktop:max-w-[82.5rem] desktop:flex desktop:justify-between tablet:grid tablet:grid-cols-2 tablet:grid-rows-2 tablet:gap-20 tablet:justify-items-center max-tablet:flex max-tablet:flex-col max-tablet:gap-15'>
            <StatisticWidget
              key='first'
              icon={<ApprovalTickIcon color='#FF4714' className='tablet:w-14 tablet:h-14' />}
              title='Her Gün 50+ Özel Haber'>
              Güvenilir kaynaklardan derlenmiş, farklı kategorilerdeki en güncel ve özel haberlere anında ulaşın.
            </StatisticWidget>
            <StatisticWidget
              key='second'
              icon={<TwoPeopleIcon color='#80A90A' className='tablet:w-14 tablet:h-14' />}
              title='Büyüyen Okur Topluluğu'>
              Her ay 10 binden fazla kişiden oluşan ve sürekli genişleyen topluluğumuza katılarak gündemi birlikte takip
              edin.
            </StatisticWidget>
            <div className='tablet:col-span-2 tablet:flex tablet:justify-center'>
              <StatisticWidget
                key='third'
                icon={<InterfaceIcon color='#FF4714' className='tablet:w-14 tablet:h-14' />}
                title='Kullanıcı Dostu Arayüz'>
                Göz yormayan, modern ve sade tasarımımız sayesinde haberleri okurken keyifli ve akıcı bir deneyim
                yaşayın.
              </StatisticWidget>
            </div>
          </div>
        </Appear>
      </section>

      <section className='w-full desktop:pb-48 tablet:pb-40 max-tablet:pb-30'>
        <Appear className='w-full' late>
          <div className='w-full mx-auto px-10 desktop:max-w-[82.5rem] flex flex-col items-start tablet:flex-row desktop:justify-center desktop:gap-15 max-desktop:gap-15'>
            <CategoryCard
              key='first'
              title="Türkiye'nin Globalleşen Yapay Zeka Girişimi Rook AI!"
              link='https://www.youtube.com/watch?v=DbvP4Y26W5M'
              customContent={true}
              content={
                <iframe
                  className='w-full h-full'
                  src='https://www.youtube.com/embed/DbvP4Y26W5M'
                  title='Yapay Zeka ile Geleceğe Yolculuk'
                />
              }
              buttonText='İzle'
            />
            <CategoryCard
              key='second'
              color='secondary'
              title='Türkiye’de AI Trendleri ve Yenilikler'
              link='https://www.youtube.com/watch?v=wjZofJX0v4M&pp=ygUDTExN'
              customContent={true}
              content={
                <iframe
                  className='w-full h-full'
                  src='https://www.youtube.com/embed/wjZofJX0v4M'
                  title='Türkiye’de AI Trendleri ve Yenilikler'
                />
              }
              buttonText='İzle'
            />
          </div>
        </Appear>
      </section>

      <section className='w-full desktop:pb-48 tablet:pb-40 max-tablet:pb-30'>
        <div className='flex flex-col w-full px-10 mx-auto desktop:max-w-[82.5rem] desktop:gap-10 tablet:gap-20 max-tablet:gap-15'>
          <Appear className='w-full'>
            <div className='flex flex-row justify-between'>
              <Title
                as='h4'
                size={null}
                className='desktop:text-[40px] desktop:leading-[48px] tablet:text-[34px] tablet:leading-[41px] max-tablet:text-[30px] max-tablet:leading-[36px]'>
                Son Dakika Haberleri
              </Title>
              <Link href='/news'>
                <Button
                  size='lg'
                  color='outlineBlack'
                  icon={<ArrowRight strokeWidth={2.65} />}
                  className='group cursor-pointer whitespace-nowrap'
                  iconClassName='transition-transform duration-300 -rotate-45 group-hover:rotate-0'
                  iconPosition='right'>
                  Hepsini Gör
                </Button>
              </Link>
            </div>
          </Appear>
          <Appear className='w-full' late options={{ threshold: 0.1 }}>
            <div className='mx-auto gap-8 place-items-center justify-between desktop:flex desktop:flex-row max-desktop:grid-cols-2 tablet:grid max-tablet:flex max-tablet:flex-col'>
              {[...cardNewsData]
                .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                .slice(0, ROW_GRID_SIZE)
                .map((card, i) => (
                  <Card
                    key={String(i)}
                    type='primary'
                    link={`news/${card.slug}`}
                    title={card.title}
                    tags={card.tags}
                    date={card.date}
                    writer={card.writer}
                    description={card.description}
                    image={card.image}
                  />
                ))}
            </div>
          </Appear>
        </div>
      </section>

      <section className='w-full desktop:pb-48 tablet:pb-40 max-tablet:pb-30'>
        <div className='flex flex-col w-full px-10 mx-auto desktop:max-w-[82.5rem] desktop:gap-10 tablet:gap-20 max-tablet:gap-15'>
          <Appear className='w-full'>
            <div className='flex flex-row justify-between'>
              <Title
                as='h4'
                size={null}
                className='desktop:text-[40px] desktop:leading-[48px] tablet:text-[34px] tablet:leading-[41px] max-tablet:text-[30px] max-tablet:leading-[36px]'>
                Haberler
              </Title>
              <Link href='/news'>
                <Button
                  color='outlineBlack'
                  icon={<ArrowRight strokeWidth={2.65} />}
                  className='group cursor-pointer whitespace-nowrap py-3'
                  iconClassName='transition-transform duration-300 -rotate-45 group-hover:rotate-0'
                  iconPosition='right'>
                  Hepsini Gör
                </Button>
              </Link>
            </div>
          </Appear>
          <Appear className='w-full' late options={{ threshold: 0.1 }}>
            <div className='w-full gap-8 place-items-center justify-center desktop:grid-cols-4 max-desktop:grid-cols-2 tablet:grid max-tablet:flex max-tablet:flex-col'>
              {cardNewsData.slice(0, ROW_GRID_SIZE).map((card, i) => (
                <Card
                  key={String(i)}
                  type='primary'
                  link={`news/${card.slug}`}
                  title={card.title}
                  tags={card.tags}
                  date={card.date}
                  writer={card.writer}
                  description={card.description}
                  image={card.image}
                />
              ))}
            </div>
          </Appear>
        </div>
      </section>

      <section className='flex flex-col justify-center items-center w-full bg-dark'>
        <div className='w-full mx-auto px-10 desktop:max-w-[82.5rem] flex flex-col justify-center items-center pt-[96px] desktop:pb-[96px] tablet:pb-[80px] max-tablet:pb-[60px] desktop:gap-[56px] tablet:gap-[40px] max-tablet:gap-[30px]'>
          <div className='w-full'>
            <Appear>
              <Title
                as='h4'
                size={null}
                color='white'
                className='desktop:text-[40px] desktop:leading-[48px] tablet:text-[34px] tablet:leading-[41px] max-tablet:text-[30px] max-tablet:leading-[36px]'>
                Vizyonumuz
              </Title>
            </Appear>
          </div>
          <Appear late>
            <TestimonialCard
              logo={<img src='/logo-group.svg' alt='logo' className='max-h-[42px] max-w-fit' width={200} height={42} />}
              name='Ayşe Yılmaz'
              position='Teknoloji Editörü'>
              Vizyonumuz, girişimcilik, teknoloji ve yapay zeka dünyasından en güncel içerikleri, global başarı
              hikayelerini ve güvenilir iş fırsatlarını bir araya getiren bir ekosistem yaratmaktır. VentureVibe olarak,
              fikirlerin hayata geçtiği, doğru insanların birbiriyle tanıştığı ve yenilikçi projelerin birlikte büyüdüğü
              bir platform olmayı hedefliyoruz.
            </TestimonialCard>
          </Appear>
        </div>
      </section>
    </div>
  )
}
