'use server'

import { Appear, Button, Input, Text } from '@venture-vibe/components/shared'
import { clsx } from '@venture-vibe/utils'
import { redirect } from 'next/navigation'
import { getUser, login } from '../../../api/users'

export default async function Login() {
  const user = await getUser()

  if (user) {
    redirect('/admin')
  }

  return (
    <div className='flex flex-col items-center justify-center font-display min-h-screen h-full bg-bg'>
      <Appear
        direction='up'
        className={clsx(
          'tablet:max-w-[500px]',
          'max-w-[90%]',
          'desktop:p-16',
          'w-full',
          'bg-white',
          'border border-platinum-stroke',
          'p-14',
          'rounded-[32px]',
        )}
        late>
        <form className={clsx('flex', 'flex-col', 'items-center', 'justify-center', 'gap-8')}>
          <div className={clsx('flex flex-col tablet:gap-4', 'w-full')}>
            <Text as='label'>Email</Text>
            <Input name='email' placeholder='<EMAIL>' size='lg' />
          </div>

          <div className={clsx('flex flex-col tablet:gap-4', 'w-full')}>
            <Text as='label'>Şifre</Text>
            <Input name='password' placeholder='***********' type='password' size='lg' />
          </div>

          <Button
            type='submit'
            color='secondary'
            size='lg'
            className='w-80 tablet:mt-10 text-md cursor-pointer mx-auto'
            formAction={login}>
            Giriş Yap
          </Button>
        </form>
      </Appear>
    </div>
  )
}
