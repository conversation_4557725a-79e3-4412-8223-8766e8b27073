'use client'

import { Button } from '@venture-vibe/components/shared'
import { useRouter } from 'next/navigation'

export default function Custom404() {
  const router = useRouter()

  return (
    <div className='flex flex-col h-screen max-h-screen justify-center items-center'>
      <div className='flex flex-col h-full gap-24 justify-center items-center desktop:max-w-[520px] tablet:max-w-[412px] max-tablet:max-w-[282px]'>
        {/** biome-ignore lint/performance/noImgElement: Reduntant */}
        <img
          src='https://framerusercontent.com/images/FiOGy1MAeTdhCoSGTz3kmYUkrU.png?scale-down-to=1024'
          alt='alt'
          width={200}
          height={200}
          className='w-full h-auto object-contain'
        />
        <Button color='black' className='cursor-pointer' onClick={() => router.back()}>
          G<PERSON>
        </Button>
      </div>
    </div>
  )
}
