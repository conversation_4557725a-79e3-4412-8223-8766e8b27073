'use server'

import process from 'node:process'
import { headers, cookies as headersCookies } from 'next/headers'

export const getToken = async () => {
  const cookies = await headersCookies()

  const token = cookies.get('ozaco-token')?.value

  if (!token) {
    return null
  }

  return token
}

export const removeToken = async () => {
  const cookies = await headersCookies()
  cookies.delete('ozaco-token')
}

export const getLang = async () => {
  const cookies = await headersCookies()

  const customLang = cookies.get('ozaco-lang')?.value

  const headersList = await headers()
  const defaultLocale = headersList.get('accept-language')?.split(',')[0]?.split('-')[0]?.toLowerCase()
  const lang = customLang || defaultLocale || 'en'

  return lang
}

export const setLanguage = async (lang: string) => {
  const cookies = await headersCookies()
  cookies.set('ozaco-lang', lang, {
    httpOnly: false,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24, // 1 day
  })
}
