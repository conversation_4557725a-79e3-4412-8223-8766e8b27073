'use server'

import process from 'node:process'
import { redirect } from 'next/navigation'
import { getToken } from './base'

export const request = async (
  path: string,
  init: (RequestInit & { skipAuth?: boolean }) | undefined = {
    skipAuth: false,
  },
): Promise<Response | null> => {
  const token = await getToken()

  if (!(token || init?.skipAuth)) {
    return null
  }

  try {
    const result = await fetch(`http://${process.env.NODE_ENV === 'production' ? 'server' : 'localhost'}:3003${path}`, {
      ...init,
      headers: {
        ...init.headers,
        // biome-ignore lint/style/useNamingConvention: Redundant
        Authorization: `Bearer ${token}`,
      },
    })

    if (!result.ok) {
      throw await result.json()
    }

    return result
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: return
    console.log(error)

    if ((error as Error)?.message?.includes(`"Unauthorized" is not valid JSON`)) {
      redirect('/api/logout')
    }

    return null
  }
}
