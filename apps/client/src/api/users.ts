'use server'

import process from 'node:process'
import { cookies as headersCookies } from 'next/headers'
import { redirect } from 'next/navigation'
import { request } from './request'

export const getUser = async () => {
  const user = (await request('/auth'))?.json()

  if (!user) {
    return null
  }

  return user
}

export const login = async (formData: FormData) => {
  const email = formData.get('email') as string
  const password = formData.get('password') as string

  const response = await request('/auth/login', {
    method: 'POST',
    body: JSON.stringify({ email, password }),
    headers: {
      'Content-Type': 'application/json',
    },
    skipAuth: true,
  })

  if (!response) {
    throw new Error('Invalid credentials')
  }

  const cookies = await headersCookies()
  const token = (await response.json())?.token

  if (!token) {
    throw new Error('Invalid credentials')
  }

  cookies.set('ozaco-token', token, {
    httpOnly: false,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24, // 1 day
  })

  return redirect('/admin')
}
