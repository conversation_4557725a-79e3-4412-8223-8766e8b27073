import { arktypeValidator } from '@hono/arktype-validator'
import { type } from 'arktype'

import { app } from '../app'
import { minio } from '../drivers'

app.get(
  '/translations/:name/:lang?',
  arktypeValidator('param', type({ name: 'string', lang: type("'en' | 'tr'").optional() })),
  async ctx => {
    const { name, lang } = await ctx.req.valid('param')

    const exists = await minio.exists(`${name}.json`)

    if (!exists) {
      return ctx.text('Not found', 404)
    }

    const file = minio.file(`${name}.json`)
    const json = await file.json()

    if (!(lang && json[lang])) {
      return Response.json(json)
    }

    return Response.json(json[lang])
  },
)
