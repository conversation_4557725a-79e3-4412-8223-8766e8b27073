import { app } from '../app'
import { minio } from '../drivers'

app.get('files/:name', async ctx => {
  const name = ctx.req.param('name')

  if (!name) {
    return ctx.text('Name is required', 404)
  }

  const exists = await minio.exists(name)

  if (!exists) {
    return ctx.text('Not found', 404)
  }

  const file = minio.file(name)
  const stats = await file.stat()

  return new Response(file.stream(), {
    headers: {
      'Content-Type': stats.type,
    },
  })
})
