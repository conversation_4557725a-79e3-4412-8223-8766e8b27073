import { arktypeValidator } from '@hono/arktype-validator'
import { type } from 'arktype'
import { sign } from 'hono/jwt'

import { app } from '../app'
import { ENV } from '../const'
import { postgres } from '../drivers'
import { jwt } from '../utils/middlewares'

app.get('auth', jwt, ctx => {
  const user = ctx.get('jwtPayload')

  return Response.json(user)
})

app.post('auth/login', arktypeValidator('json', type({ email: 'string', password: 'string' })), async ctx => {
  const { email, password: rawPassword } = await ctx.req.valid('json')

  const users: { id: number; email: string; password: string; login: number }[] =
    await postgres`SELECT * FROM users WHERE email = ${email};`

  if (users.length === 0) {
    return ctx.text('Invalid credentials', 401)
  }

  const matchingUser = users.find(user => Bun.password.verifySync(rawPassword, user.password))

  if (!matchingUser) {
    return ctx.text('Invalid credentials', 401)
  }

  Reflect.deleteProperty(matchingUser, 'password')
  matchingUser.login = Date.now()

  return Response.json({
    ...matchingUser,
    token: await sign(matchingUser, ENV.jwtSecret),
  })
})
