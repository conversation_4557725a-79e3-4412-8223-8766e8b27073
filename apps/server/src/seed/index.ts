import { ENV } from '../const'
import { getVersion } from '../utils/db'

import { settings } from './settings'
import { translations } from './translations'
import { users } from './users'

if (ENV.mode !== 'production') {
  await translations('999.999.999', true)
  await users('999.999.999', true)
  await settings('999.999.999', true)
}

await settings(await getVersion('settings'))
await users(await getVersion('users'))
await translations(await getVersion('translations'))
