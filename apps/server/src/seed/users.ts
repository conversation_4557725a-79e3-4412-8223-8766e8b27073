/** biome-ignore-all lint/suspicious/noConsole: Redundant */
import { postgres } from '../drivers'
import { migrate } from '../utils/db'

export const users = migrate([
  '0.0.1',
  async () => {
    console.log('Applying users')

    await postgres`
      CREATE TABLE users (
        id SERIAL PRIMARY KEY,
        email TEXT NOT NULL,
        name TEXT NOT NULL,
        password TEXT NOT NULL
      );
    `

    await postgres`
      INSERT INTO settings (key, value) VALUES ('users-version', '0.0.1');
    `

    const password = await Bun.password.hash('roottoor123', 'argon2id')

    await postgres`
      INSERT INTO users (email, name, password) VALUES ('<EMAIL>', 'root', ${password});
    `
  },
  () => {
    console.log('Reverting users')

    return postgres`
      DROP TABLE users;
    `
  },
])
