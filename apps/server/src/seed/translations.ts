/** biome-ignore-all lint/suspicious/noConsole: Redundant */
import { minio, postgres } from '../drivers'
import { migrate } from '../utils/db'
import { common } from './translations/common'

export const translations = migrate([
  '0.0.1',
  async () => {
    console.log('Applying translations')

    const commonExists = await minio.exists('common.json')

    if (!commonExists) {
      await minio.write('common.json', JSON.stringify(common))
    }

    await postgres`
      INSERT INTO settings (key, value) VALUES ('translations-version', '0.0.1');
    `
  },
  async () => {
    console.log('Reverting translations')

    const commonExists = await minio.exists('common.json')

    if (commonExists) {
      await minio.delete('common.json')
    }

    await postgres`
      DELETE FROM settings WHERE key = 'translations-version';      
    `
  },
])
