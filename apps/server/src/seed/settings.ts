/** biome-ignore-all lint/suspicious/noConsole: Redundant */
import { postgres } from '../drivers'
import { migrate } from '../utils/db'

export const settings = migrate([
  '0.0.1',
  async () => {
    console.log('Applying settings')

    await postgres`
      CREATE TABLE settings (
        id SERIAL PRIMARY KEY,
        key TEXT NOT NULL,
        value TEXT NOT NULL
      );
    `

    await postgres`
      INSERT INTO settings (key, value) VALUES ('settings-version', '0.0.1');
    `
  },
  () => {
    console.log('Reverting settings')

    return postgres`
      DROP TABLE settings;
    `
  },
])
