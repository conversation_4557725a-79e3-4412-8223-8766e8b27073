import { S3Client, SQL } from 'bun'
import { ENV } from './const'

export const postgres = new SQL(
  `postgres://root:roottoor123@${ENV.mode === 'production' ? 'postgres' : 'localhost'}:5432/venturevibe`,
)

export const minio = new S3Client({
  accessKeyId: 'root',
  secretAccessKey: 'roottoor123',
  bucket: 'venturevibe',
  endpoint: `http://${ENV.mode === 'production' ? 'minio' : 'localhost'}:9000`,
  retry: Number.POSITIVE_INFINITY,
})
