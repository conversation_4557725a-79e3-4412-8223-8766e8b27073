/** biome-ignore-all lint/style/useNamingConvention: Redundant */

import type { Promisify } from '@venture-vibe/utils'
import { semver } from 'bun'
import { postgres } from '../drivers'

export interface Table {
  schemaname: string
  tablename: string
  tableowner: string
  tablespace: string
  hasindexes: boolean
  hasrules: boolean
  hastriggers: boolean
  rowsecurity: boolean
}

export interface Column {
  table_name: string
  column_name: string
  data_type: string
  is_nullable: string
  column_default: string
  character_maximum_length: number
  numeric_precision: number
  numeric_scale: number
}

export const getTables = async (): Promise<Table[]> => {
  const tables = await postgres`
    SELECT * FROM pg_catalog.pg_tables
    WHERE schemaname != 'information_schema' AND
    schemaname != 'pg_catalog';
  `
  return tables
}

export const getColumns = async (table: string) => {
  const columns = await postgres`
    SELECT * FROM information_schema.columns
    WHERE table_name = ${table};
  `
  return columns
}

export const getVersion = async (target: string): Promise<string> => {
  const settingsExists = (await getTables()).find(table => table.tablename === 'settings')

  if (!settingsExists) {
    return '0.0.0'
  }

  const version = await postgres`SELECT value FROM settings WHERE key = ${`${target}-version`};`
  return version[0]?.value || '0.0.0'
}

export const migrate = (...migrations: [version: string, up: () => Promisify<void>, down: () => Promisify<void>][]) => {
  const sorted = migrations.sort((a, b) => semver.order(a[0], b[0]))

  return async (currentVersion: string, down = false) => {
    const targetMigrations = down ? sorted.toReversed() : [...sorted]

    for (const migration of targetMigrations) {
      try {
        const order = semver.order(currentVersion, migration[0])

        if (down) {
          if (order === 1 || order === 0) {
            await migration[2]()
          }

          continue
        }

        if (order === -1) {
          await migration[1]()
        }
      } catch (error) {
        // biome-ignore lint/suspicious/noConsole: Redundant
        console.error(error)
      }
    }

    return migrations
  }
}
