language: "typescript"
type: "library"
platform: "bun"

tasks:
  dev-builder:
    local: true
    command: "bunx ozaco build -t bun -w"
    deps: ["utils:dev"]
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
  dev:
    local: true
    command: "bun run --watch src/index.ts"
    deps: ["server:dev-builder"]
    inputs:
      - "dist"
      - "src"
  build:
    command: "bunx ozaco build -t bun -s --env production"
    deps: ["utils:build"]
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
  start:
    local: true
    deps: ["server:build"]
    command: "bun run dist/index.js"
    inputs:
      - "dist"
  docker:
    command: "bun run dist/index.js"
    local: true
    inputs:
      - "dist"